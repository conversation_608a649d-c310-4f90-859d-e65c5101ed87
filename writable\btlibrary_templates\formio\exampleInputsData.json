{"builder": {"basic": false, "advanced": false, "data": false, "premium": false, "layout": false, "builderFormFields": {"title": "Form Fields", "default": true, "weight": 0, "components": {"formTitle": {"title": "Form Title", "key": "formTitle", "icon": "font", "weight": 0, "schema": {"tag": "h2", "label": "Form Title", "attrs": [], "content": "Title of Form", "refreshOnChange": false, "key": "formTitle", "type": "htmlelement", "input": false, "tableView": false, "btwizard": {"type": "form_title"}, "hidden_tabs": ["api", "logic", "layout"]}}, "section": {"title": "Panel: <PERSON><PERSON>", "key": "section", "icon": "list-alt", "weight": 0, "schema": {"title": "Slide X", "tableView": false, "type": "panel", "key": "section", "input": false, "property": "section"}}, "text": {"title": "Text", "key": "text", "icon": "terminal", "weight": 1, "schema": {"label": "Text Field", "description": "[FIELD X]", "tableView": false, "type": "textfield", "key": "text", "input": true, "validate": {"maxLength": 255}, "btwizard": {"type": "text"}, "hidden_tabs": ["api", "logic", "layout"], "show_inputs": [" // All inputs are shown", "label", "labelPosition", "placeholder", "description", "tooltip", "prefix", "suffix", "widget.type", "inputMask", "displayMask", "allowMultipleMasks", "customClass", "tabindex", "autocomplete", "hidden", "<PERSON><PERSON><PERSON><PERSON>", "showWordCount", "showCharCount", "mask", "autofocus", "spellcheck", "disabled", "tableView", "modalEdit", "multiple", "defaultValue", "persistent", "inputFormat", "protected", "dbIndex", "case", "truncateMultipleSpaces", "encrypted", "redrawOn", "clearOnHide", "customDefaultValuePanel", "calculateValuePanel", "calculateServer", "allowCalculateOverride", "validateOn", "validate.required", "unique", "validate.min<PERSON><PERSON>th", "validate.max<PERSON>ength", "validate.minWords", "validate.max<PERSON><PERSON>s", "validate.pattern", "error<PERSON><PERSON><PERSON>", "validate.customMessage", "custom-validation-js", "json-validation-json", "errors", "key", "tags", "properties", "simple-conditional", "conditional.show", "conditional.when", "conditional.eq", "customConditionalPanel", "logic", "attributes", "overlay"], "descriptionField": {"type": "text", "placeholder": "[FIELD X]", "value": "test2", "required": true}}, "components": [{"key": "data", "ignore": true}]}, "number": {"title": "Number", "key": "number", "icon": "hashtag", "weight": 2, "schema": {"label": "Number", "description": "[FIELD X]", "mask": false, "tableView": false, "delimiter": false, "requireDecimal": false, "inputFormat": "plain", "truncateMultipleSpaces": false, "key": "number", "type": "number", "input": true, "btwizard": {"type": "number"}, "hidden_tabs": ["api", "logic"], "hidden_inputs": [" // All inputs are hidden", "label", "labelPosition", "placeholder", "description", "tooltip", "prefix", "suffix", "widget.type", "inputMask", "displayMask", "allowMultipleMasks", "customClass", "tabindex", "autocomplete", "hidden", "<PERSON><PERSON><PERSON><PERSON>", "showWordCount", "showCharCount", "mask", "autofocus", "spellcheck", "disabled", "tableView", "modalEdit", "multiple", "defaultValue", "persistent", "inputFormat", "protected", "dbIndex", "case", "truncateMultipleSpaces", "encrypted", "redrawOn", "clearOnHide", "customDefaultValuePanel", "calculateValuePanel", "calculateServer", "allowCalculateOverride", "validateOn", "validate.required", "unique", "validate.min<PERSON><PERSON>th", "validate.max<PERSON>ength", "validate.minWords", "validate.max<PERSON><PERSON>s", "validate.pattern", "error<PERSON><PERSON><PERSON>", "validate.customMessage", "custom-validation-js", "json-validation-json", "errors", "key", "tags", "properties", "simple-conditional", "conditional.show", "conditional.when", "conditional.eq", "customConditionalPanel", "logic", "attributes", "overlay"]}}}}}}