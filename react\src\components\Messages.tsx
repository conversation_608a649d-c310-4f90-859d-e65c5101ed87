import UserChat from "./UserChat";
import AiResponse from "./AiResponse";

import type { Message } from "../types/message";

export default function Messages({ messages }: { messages: Message[] }) {
  return (
    <div className="messages scroll-1 mt-[68px] flex w-full flex-col gap-[35px] overflow-y-auto">
      {messages.map((message, index) => (
        <>
          {message.role === "user" ? (
            <UserChat message={message} key={index} />
          ) : (
            <AiResponse message={message} key={index} />
          )}
        </>
      ))}
    </div>
  );
}
