{"builder": {"basic": false, "advanced": false, "data": false, "premium": false, "layout": false, "builderFormFields": {"title": "Form Components", "default": true, "weight": 0, "components": {"section": {"title": "Section", "key": "panelSlide", "icon": "list-alt", "weight": 0, "schema": {"title": "Section X", "tableView": false, "type": "panel", "input": false, "btwizard": {"type": "section"}, "show_inputs": ["title", "hidden", "<PERSON><PERSON><PERSON><PERSON>", "valueProperty", "key", "simple-conditional", "conditional.show", "conditional.when", "conditional.eq", "customConditionalPanel"]}}, "text": {"title": "Text", "key": "text", "icon": "terminal", "weight": 1, "schema": {"label": "Text Field", "description": "[FIELD X]", "tableView": false, "type": "textfield", "key": "text", "input": true, "validate": {"maxLength": 255}, "btwizard": {"type": "text"}, "show_inputs": ["label", "key", "placeholder", "description", "<PERSON><PERSON><PERSON><PERSON>", "defaultValue", "simple-conditional", "conditional.show", "conditional.when", "conditional.eq", "customConditionalPanel", "validate.min<PERSON><PERSON>th", "validate.max<PERSON>ength", "customDefaultValuePanel", "customDefaultValue-js"]}, "components": [{"key": "data", "ignore": true}]}, "number": {"title": "Number", "key": "number", "icon": "hashtag", "weight": 2, "schema": {"label": "Number", "description": "[FIELD X]", "mask": false, "tableView": false, "delimiter": false, "requireDecimal": false, "inputFormat": "plain", "truncateMultipleSpaces": false, "key": "number", "type": "number", "input": true, "btwizard": {"type": "number"}, "show_inputs": ["label", "placeholder", "description", "hidden", "<PERSON><PERSON><PERSON><PERSON>", "defaultValue", "key", "simple-conditional", "conditional.show", "conditional.when", "conditional.eq", "customConditionalPanel", "customDefaultValuePanel", "customDefaultValue-js", "calculateValuePanel", "calculateValue-js"]}}, "dropdown": {"title": "Dropdown", "key": "dropdown", "icon": "list", "weight": 3, "schema": {"label": "Dropdown", "description": "[FIELD X]", "tableView": false, "type": "select", "key": "dropdown", "input": true, "btwizard": {"type": "dropdown"}, "show_inputs": ["label", "placeholder", "description", "hidden", "<PERSON><PERSON><PERSON><PERSON>", "defaultValue", "key", "simple-conditional", "conditional.show", "conditional.when", "conditional.eq", "customConditionalPanel"]}}, "date": {"title": "Date", "key": "date", "icon": "calendar", "weight": 4, "schema": {"label": "Date", "description": "[FIELD X]", "format": "MMMM dd, yyyy", "tableView": false, "type": "datetime", "key": "date", "input": true, "enableTime": false, "property": "date", "btwizard": {"type": "date"}, "show_inputs": ["label", "placeholder", "description", "hidden", "<PERSON><PERSON><PERSON><PERSON>", "defaultValue", "key", "simple-conditional", "conditional.show", "conditional.when", "conditional.eq", "customConditionalPanel", "format"]}}, "textarea": {"title": "Textarea", "key": "textarea", "icon": "font", "weight": 5, "schema": {"label": "Textarea", "description": "[FIELD X]", "tableView": false, "type": "textarea", "key": "textarea", "input": true, "btwizard": {"type": "textarea"}, "hidden_tabs": ["date", "time"], "show_inputs": ["label", "placeholder", "description", "hidden", "<PERSON><PERSON><PERSON><PERSON>", "defaultValue", "key", "simple-conditional", "conditional.show", "conditional.when", "conditional.eq", "customConditionalPanel", "validate.min<PERSON><PERSON>th", "validate.max<PERSON>ength", "customDefaultValuePanel", "customDefaultValue-js"]}}, "radio": {"title": "Radio", "key": "radio", "icon": "dot-circle-o", "weight": 6, "schema": {"label": "Radio", "optionsLabelPosition": "right", "description": "[FIELD X]", "inline": false, "tableView": false, "values": [], "type": "radio", "key": "radio", "input": true, "btwizard": {"type": "radio"}, "show_inputs": ["label", "placeholder", "description", "hidden", "<PERSON><PERSON><PERSON><PERSON>", "defaultValue", "key", "simple-conditional", "conditional.show", "conditional.when", "conditional.eq", "customConditionalPanel"]}}, "checkbox": {"title": "Checkbox", "key": "checkbox", "icon": "check-square", "weight": 7, "schema": {"label": "Checkbox", "description": "[FIELD X]", "inline": false, "tableView": false, "values": [], "type": "checkbox", "key": "checkbox", "input": true, "defaultValue": false, "btwizard": {"type": "checkbox"}, "show_inputs": ["label", "placeholder", "description", "hidden", "<PERSON><PERSON><PERSON><PERSON>", "defaultValue", "key", "simple-conditional", "conditional.show", "conditional.when", "conditional.eq", "customConditionalPanel"]}}, "signature": {"title": "Signature", "key": "signature", "icon": "pencil", "weight": 8, "schema": {"label": "Signature", "description": "[FIELD X]", "tableView": false, "type": "signature", "key": "signature", "input": true, "btwizard": {"type": "submit"}, "show_inputs": ["label", "placeholder", "description", "hidden", "<PERSON><PERSON><PERSON><PERSON>", "defaultValue", "key", "simple-conditional", "conditional.show", "conditional.when", "conditional.eq", "customConditionalPanel", "customDefaultValuePanel", "customDefaultValue-js", "footer"]}}, "us_state": {"title": "US State", "key": "us_state", "icon": "th-list", "weight": 9, "schema": {"label": "US State", "description": "[FIELD X]", "widget": "html5", "tableView": false, "dataSrc": "custom", "key": "us_state", "type": "select", "input": true, "btwizard": {"type": "us_state"}, "show_inputs": ["label", "placeholder", "description", "hidden", "<PERSON><PERSON><PERSON><PERSON>", "defaultValue", "key", "simple-conditional", "conditional.show", "conditional.when", "conditional.eq", "customConditionalPanel"]}}, "heading": {"title": "Heading", "key": "heading", "icon": "code", "weight": 10, "schema": {"label": "Heading", "attrs": [], "content": "Please review your document before submitting.", "refreshOnChange": false, "key": "heading", "type": "htmlelement", "input": false, "tableView": false, "btwizard": {"type": "heading"}, "show_inputs": ["label", "content", "hidden", "key", "simple-conditional", "conditional.show", "conditional.when", "conditional.eq", "customConditionalPanel"]}}}}, "builderLtr2Pdf": {"title": "Content Components", "default": true, "components": {"ltr2pdfPage": {"title": "Page", "key": "panelPage", "icon": "list-alt", "weight": 0, "schema": {"title": "New Page", "tableView": false, "type": "panel", "input": false, "refreshOnChange": true, "btwizard": {"type": "ltr2pdfPage"}, "show_inputs": ["title", "hidden", "<PERSON><PERSON><PERSON><PERSON>", "valueProperty", "key", "simple-conditional", "conditional.show", "conditional.when", "conditional.eq", "customConditionalPanel"]}}, "ltr2pdfTitle": {"title": "Title", "key": "ltr2pdfTitle", "icon": "font", "weight": 10, "schema": {"label": "Title of Form", "customClass": "form-header", "html": "<h2 style=\"text-align:center;\"><span style=\"font-family:'Times New Roman', Times, serif;\"><strong>TITLE &nbsp;</strong></span></h2>", "tableView": false, "type": "content", "input": false, "refreshOnChange": true, "btwizard": {"type": "ltr2pdfTitle"}, "hidden_tabs": ["logic", "layout"], "hidden_inputs": ["customClass", "refreshOnChange", "modalEdit", "tags", "properties"]}}, "ltr2pdfContent": {"title": "Content", "key": "ltr2pdfContent", "icon": "font", "weight": 20, "schema": {"label": "Content", "customClass": "form-body", "html": "<p><span style=\"font-family:'Times New Roman', Times, serif;\">Paragraph X</span></p>", "tableView": false, "type": "content", "input": false, "refreshOnChange": true, "btwizard": {"type": "ltr2pdfContent"}, "hidden_tabs": ["logic", "layout"], "hidden_inputs": ["customClass", "refreshOnChange", "modalEdit", "tags", "properties"]}}}}}}