<?php

namespace App\Controllers\ArtificialIntelligence;

use App\Controllers\ArtificialIntelligence\_BaseArtificialIntelligence as AIBase;
use App\Controllers\ArtificialIntelligence\ApiV2;


class Builder extends AIBase
{
    private $themeId = 'aiBuilder';
    private $themePageVersion = 'C01';
    private $themeSlug = null;

    public function __construct()
    {
        parent::__construct();
    }

    public function index($slug = 'custom')
    {
        $this->themeSlug = $slug;

        $this->theme_data();
        $this->theme_pageVersion();
        $this->theme_avalanche();
    }

    //---------------------------------------------------------------------------------------------
    //  private
    //---------------------------------------------------------------------------------------------

    private function theme_avalanche()
    {
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData
        ];

        $this->bttwig->view("/avalanche/{$this->themeId}/page_{$this->themeId}_{$this->themePageVersion}.twig", $viewData);
    }


    private function theme_data()
    {
        $date_now = date('Y-m-d H:i:s', time());
        $btForm = [
            'page_title' => 'Translate from English',
            'submit_label' => 'Generate',
            'textfield_label' => 'Prompt',
            'textAppId' => btutilGenerateUuid()
        ];

        $btForm['formio_data'] = file_get_contents(APPPATH . "Libraries/ai/formio_builder/data/data-template-01.json");
        $btForm['formio_form'] = file_get_contents(APPPATH . "Libraries/ai/formio_builder/forms/form-template-01.json");
        $btForm['formio_options'] = file_get_contents(APPPATH . "Libraries/ai/formio_builder/options/option-template-01.json");



        $this->themePageData = [
            'body_class' => 'app',
            'page_title' => '',
            'meta_data' => [
                'description' => '',
                // 'robots' => 'noindex, follow', //uncomment and change as needed
            ],
            'slug' => $this->themeSlug,
            'btform' => $btForm
        ];
    }

    private function theme_pageVersion()
    {
        // $this->themePageVersion = '01C';
        // $pageVer = btflag_get('v',  $pageVer);

        return $this->themePageVersion;
    }

}
