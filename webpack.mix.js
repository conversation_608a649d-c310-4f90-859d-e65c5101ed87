let mix = require('laravel-mix');
require('laravel-mix-polyfill');
const TargetsPlugin = require('targets-webpack-plugin');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for your application, as well as bundling up your JS files.
 |
 */
// mix.js('public/assets/src/vue/admin/app.js', 'public/dist/js/admin').polyfill({
//     enabled: true,
//     useBuiltIns: "usage",
//     targets: {"firefox": "50", "ie": 11}
// });

mix.js('public/assets/src/vue/admin/formslibrary/app.js', 'public/dist/js/admin/formslibrary').polyfill({
    enabled: true,
    useBuiltIns: "usage",
    targets: {"firefox": "50", "ie": 11}
});