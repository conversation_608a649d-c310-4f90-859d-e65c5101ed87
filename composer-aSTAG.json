{"name": "codebay/btapp", "description": "BTApp", "type": "project", "minimum-stability": "dev", "prefer-stable": true, "repositories": [{"url": "*********************:baytech/btcore.git", "type": "vcs", "require-all": true}, {"url": "*********************:baytech/btcomponents.git", "type": "vcs", "require-all": false}], "require": {"btapp/btcore": "dev-01-aSTAG-AI", "codebay/btcomponents": "dev-01-aSTAG-app_aipro_org", "php": "^8.0", "codeigniter4/framework": "4.3.8", "mobiledetect/mobiledetectlib": "^2.8.41", "guzzlehttp/guzzle": "^6.0", "sentry/sdk": "^3.3"}, "require-dev": {"fakerphp/faker": "^1.9", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": "^8.5"}, "suggest": {"ext-fileinfo": "Improves mime type detection for files"}, "autoload": {"psr-4": {"App\\": "app", "Config\\": "app/Config"}, "exclude-from-classmap": ["**/Database/Migrations/**"]}, "config": {"allow-plugins": {"php-http/discovery": false}}}