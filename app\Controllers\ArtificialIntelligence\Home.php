<?php

namespace App\Controllers\ArtificialIntelligence;

use App\Controllers\ArtificialIntelligence\_BaseArtificialIntelligence as AIBase;

class Home extends AIBase
{
    private $themeId = 'aiHome';
    private $themePageVersion = '01A';
    private $themeSlug = null;

    public function __construct()
    {
        parent::__construct();
    }

    public function index($slug = '')
    {
        // btutilDebug($slug);
        $this->themeSlug = $slug;

        $this->theme_data();
        $this->theme_pageVersion();
        $this->theme_avalanche();
    }



    //---------------------------------------------------------------------------------------------
    //  private
    //---------------------------------------------------------------------------------------------

    private function theme_avalanche()
    {
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData
        ];
        // btutilDebug($viewData);

        $this->bttwig->view("/avalanche/{$this->themeId}/page_{$this->themeId}_{$this->themePageVersion}.twig", $viewData);
    }

    private function theme_data()
    {
        $this->themePageData = [
            'assets_path' => '',
            'page_title' => '',
            'meta_data' => [
                'description' => '',
                // 'robots' => 'noindex, follow', //uncomment and change as needed
            ],
            'slug' => $this->themeSlug
        ];
    }

    private function theme_pageVersion()
    {
        $this->themePageVersion = '01C';
        // $pageVer = btflag_get('v',  $pageVer);

        return $this->themePageVersion;
    }

}
