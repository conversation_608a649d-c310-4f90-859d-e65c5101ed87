<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use <PERSON>Igniter\HTTP\CLIRequest;
use <PERSON>Igniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;

/**
 * Custom
 */
use BT<PERSON>ore\TemplateEngine as BTTemplateEngine;
use BTCore\TemplateEngine\TwigCustom as BTTwig;
use BTCore\FileStorage as BTFileStorage;
use BTCore\FileStorage\FlySystemLocal as BTFileStorageFsLocal; //todo: not used
use BTCore\FileStorage\FlySystemS3 as BTFileStorageFsS3;

// use BTCore\Converter as BTConverter;
// use BTCore\Converter\BtwizardToFormio as BTBtwizardToFormio;
// use BTCore\Converter\FormioToBtwizard as BTFormioToBtwizard;

/**
 * Class BaseController
 *
 * BaseController provides a convenient place for loading components
 * and performing functions that are needed by all your controllers.
 * Extend this class in any new controllers:
 *     class Home extends BaseController
 *
 * For security be sure to declare any new methods as protected or private.
 */
abstract class BaseController extends Controller
{
    /**
     * Instance of the main Request object.
     *
     * @var CLIRequest|IncomingRequest
     */
    protected $request;

    /**
     * An array of helpers to be loaded automatically upon
     * class instantiation. These helpers will be available
     * to all other controllers that extend BaseController.
     *
     * @var array
     */
    protected $helpers = ['btflag', 'btsession', 'btutil', 'btenum'];

    /**
     * Be sure to declare properties for any property fetch you initialized.
     * The creation of dynamic property is deprecated in PHP 8.2.
     */

    protected $session;

    /**
     * Custom
     */
    protected $debugMode = (ENVIRONMENT == 'development');

    protected $themeBaseData = [];
    protected $themePageData = [];

    protected $bttwig;
    protected $bttwig_data = [];

    protected $encryptedFlag = [
        'dynamic_prompts' => 'WcvYPABR'
    ];
    protected $fileStorage;

    // protected $BTConverter;
    // protected $BtwizardToFormio;
    // protected $BTFormioToBtwizard;

    /**
     * Undocumented variable
     *
     * @var array
     */
    protected $appResponse = [
        'success' => 0,
        'message' => 'Something went wrong. Please try again later.',
        'data' => ''
    ];

    /**
     * Constructor.
     */
    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        // Do Not Edit This Line
        parent::initController($request, $response, $logger);

        // Preload any models, libraries, etc, here.

        // E.g.: $this->session = \Config\Services::session();
        $this->session = session();

        $twig = new BTTwig(APPPATH . 'Views/twig/', WRITEPATH . 'cache/twig/');
        $this->bttwig = new BTTemplateEngine();
        $this->bttwig->_init($twig);

        $this->setThemeBaseData();

        $s3_client = [
            's3_access_key' => getenv('S3_ACCESS_KEY'),
            's3_secret_key' => getenv('S3_SECRET_KEY'),
            's3_bucket_name' => getenv('S3_BUCKET_NAME'),
            's3_region' => getenv('S3_REGION'),
            's3_version' => getenv('S3_VERSION'),
        ];
        btsessionSet('app', [
            's3_config' => $s3_client,
        ]);
        $fsS3 = new BTFileStorageFsS3($s3_client);
        $this->fileStorage = new BTFileStorage();
        $this->fileStorage->_init($fsS3);

        // $this->BTConverter = new BTConverter();
        // $this->BtwizardToFormio = new BTBtwizardToFormio();
        // $this->BTFormioToBtwizard = new BTFormioToBtwizard();



    }

    //---------------------------------------------------------------------------------------------
    //  protected
    //---------------------------------------------------------------------------------------------
    protected function getToolCount() {
        $url = getenv('API_URL') . '/e/get-total-app';
        $ch_data = [];
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($ch_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type:text/json'));
        $response = curl_exec($ch);
        curl_close($ch);

        $tool_count = getenv('TOTAL_APP') ? getenv('TOTAL_APP') : TOTAL_APP;

        return $response ? json_decode($response, true)["total"] : $tool_count;
    }


    //---------------------------------------------------------------------------------------------
    //  private
    //---------------------------------------------------------------------------------------------
    private function setThemeBaseData()
	{
        $this->bttwig_data['GLOBAL_DATA'] = [
            'BASE_URL' => base_url(),
            'BUILD' => date('mdYHis'),
            'CSS_ASSETS' => base_url() . 'assets/css/',
            'DIST_ASSETS' => base_url() . 'dist/',
            'FORCE_CACHE' => 0,
            'MAIN_APP_URL' => getenv('MAIN_APP_URL'),
            'YEAR' => date("Y"),
        ];

		$this->themeBaseData = [
            'assets_common' => base_url() . "assets/common/",
			'base_url' => base_url(),
            'build' => date('mdYHis'),
            'year' => date("Y"),
		];

        $dynamic_prompts = btflag($this->encryptedFlag['dynamic_prompts'], FLAG_DYNAMIC_PROMPT);

        btflag_set($this->encryptedFlag['dynamic_prompts'], $dynamic_prompts, ['domain' => '.ai-pro.org']);
	}
}
