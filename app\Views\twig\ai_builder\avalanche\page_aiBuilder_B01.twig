{% extends 'base_admin_A.twig' %}
{% block styles %}
  {{ parent() }}
  <link rel='stylesheet' href='https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css?ver={{ BASE.build }}' />
  <link rel='stylesheet' href='https://cdn.form.io/formiojs/formio.full.min.css?ver={{ BASE.build }}' />
  <link rel='stylesheet' href='{{ BASE.base_url }}themes/avalanche/css/builder/main.css?ver={{ BASE.build }}'>
  <style>
    .component-edit-container {
        overflow: visible !important;
    }
  </style>
{% endblock %}
{% block pre_scripts %}
  {{ parent() }}
  <script src='https://cdn.form.io/formiojs/formio.full.min.js?ver={{ BASE.build }}'></script>
  <script type="text/javascript"  src="{{ BASE.base_url }}assets/js/customFormio.js"></script>
  <script type='text/javascript'>
    var btformForm = JSON.stringify({{ PAGE.btform.formio_form | raw }});
    var btformOptions = JSON.stringify({{ PAGE.btform.formio_options | raw }});
    var form_data;

    window.onload = function () {
        Formio.builder(document.getElementById('builder'),
          JSON.parse(btformForm) ,
          JSON.parse(btformOptions) )
        .then(function (form) {
            form.on("change", function (e) {
                // console.log(JSON.stringify(e));
                localStorage.setItem("btformData", JSON.stringify(e));
            });
        });

        Formio.createForm(document.getElementById('formio'),
            {
                "components":[
                    {
                    "label": "HTML",
                    "attrs": [
                        {
                        "attr": "",
                        "value": ""
                        }
                    ],
                    "content": "{{ PAGE.page_title }}",
                    "refreshOnChange": false,
                    "key": "html",
                    "type": "htmlelement",
                    "input": false,
                    "tableView": false
                    }
                ]
            },
            {}
            ).then(function (form) {
                form.on("change", function (e) {
                    // console.log(e);
                });
            });
    };


	</script>
{% endblock %}
{% block body_header %}
  <header class="app-header fixed-top">
    <div class="app-header-inner">
      <div class="container-fluid py-2">
        <div class="app-header-content">
          <div class="row justify-content-between align-items-center">
            <div class="col-auto">
              <a id="sidepanel-toggler" class="sidepanel-toggler d-inline-block d-xl-none" href="#">
                <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewbox="0 0 30 30" role="img">
                  <title>
                    Menu


                  </title>
                  <path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div id="app-sidepanel" class="app-sidepanel sidepanel-hidden">
      <div id="sidepanel-drop" class="sidepanel-drop"></div>
      <div class="sidepanel-inner d-flex flex-column">
        <a href="#" id="sidepanel-close" class="sidepanel-close d-xl-none">
          ×




        </a>
        <div style="padding: 5px">
          <div id='formio'></div>
        </div>
        <button id="publishToDev" style="margin-right: 10px; margin-left: 10px;" class="btn btn-success" onclick="javascript:save()">
          Save



        </button>
      </div>
    </div>
  </header>
{% endblock %}
{% block body_modal %}
  {{ parent() }}
{% endblock %}
{% block body_content %}
  <div style="padding: 20px">
    <div id='builder'></div>
  </div>
{% endblock %}
{% block body_footer %}
  {{ parent() }}
{% endblock %}
{% block post_scripts %}
  {{ parent() }}
  <script src="{{ BASE.base_url }}themes/avalanche/js/app.js?ver={{ BASE.build }}"></script>
  <script type='text/javascript'>

    function save() {
      console.log('saving as draft...');


    //   sessionStorage.setItem("lastname", "Smith");


    //   console.log(localStorage.getItem('appForm'));

    //   var slug = "test";
    //   var url = '{{ BASE.base_url }}ai/crudl/update';
    //   var formData = new FormData();
    //   formData.append('data', form_data);
    //   formData.append('slug', slug);

    //   fetch(url, { method: 'POST', body: formData })
    //     .then(function (response) {
    //       return response.json();
    //     })
    //     .then(function (data) {
    //       //console.log(data);

    //       if (data.ok) {
    //         console.log('successful!');
    //       }
    //     }
    //   );


    }

  </script>
{% endblock %}
