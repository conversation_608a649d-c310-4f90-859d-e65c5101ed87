var root_path = '../../';

require('dotenv').config({ path: root_path+'.env' });

const { series, parallel } = require('gulp');
const { src, dest } = require('gulp');

var isPublish = 1;
var isDebug = 0;

var awspublish = require("gulp-awspublish");

if (isDebug) {
  console.log(process.env);
}


function publish_to_s3() {
  if (!isPublish) {
    return 1;
  }

  var publisher = awspublish.create(
    {
      region: process.env.S3_REGION,
      params: {
        "Bucket": process.env.S3_BUCKET_NAME_DEVT,
      },
      credentials: {
        "accessKeyId": process.env.S3_ACCESS_KEY,
        "secretAccessKey": process.env.S3_SECRET_KEY,
        "signatureVersion": process.env.S3_SIGNATURE_VERSION
      }
    },
    {
      cacheFileName: root_path+"writable/btlibrary_forms/logs/"+process.env.S3_BUCKET_NAME_DEVT+".published"
    }
  );

  // define custom headers
  var headers = {
    "Cache-Control": "max-age=315360000, no-transform, public"
    // ...
  };

  return (
    src(root_path+"writable/btlibrary_forms/process/**")
      // gzip, Set Content-Encoding headers and add .gz extension
      // .pipe(awspublish.gzip({ ext: ".gz" }))

      // publisher will add Content-Length, Content-Type and headers specified above
      // If not specified it will set x-amz-acl to public-read by default
      .pipe(publisher.publish(headers))

      // create a cache file to speed up consecutive uploads
      .pipe(publisher.cache())

      // print upload updates to console
      .pipe(awspublish.reporter())
  );
}

// Aug 25, 2022 - not used anymore
// exports.build = series(
//   publish_to_s3,
// );
