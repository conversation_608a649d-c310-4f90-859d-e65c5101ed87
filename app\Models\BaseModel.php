<?php

namespace App\Models;

use CodeIgniter\Model;

class BaseModel extends Model
{
    protected $DBGroup = 'default';

    protected $table      = 'users';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;

    protected $returnType     = 'array';
    protected $useSoftDeletes = true;

    protected $allowedFields = ['name', 'email'];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [];
    protected $validationMessages   = [];
    protected $skipValidation       = true;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];


    protected $apiResponse = [
        'success' => 0,
        'message' => 'Something went wrong. Please try again later.',
        'data' => []
    ];

    /**
     *  For reference, please refer to https://codeigniter.com/user_guide/models/model.html#configuring-your-model
     *
     *  https://codeigniter.com/user_guide/models/model.html#insert
     *  https://codeigniter.com/user_guide/models/model.html#update
     *  https://codeigniter.com/user_guide/models/model.html#delete
     *  https://codeigniter.com/user_guide/database/query_builder.html?highlight=count#builder-countallresults
     *
     */
}
