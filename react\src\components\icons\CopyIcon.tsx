import type { IconProps } from "./IconProps";

export default function Icon({
  className,
  width = "14",
  height = "14",
  fill = "currentColor",
  size = null,
}: IconProps) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width={size || width}
      height={size || height}
      viewBox="0 0 12 12"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3 1.5C3 1.10218 3.15804 0.720644 3.43934 0.43934C3.72064 0.158035 4.10218 0 4.5 0L10.5 0C10.8978 0 11.2794 0.158035 11.5607 0.43934C11.842 0.720644 12 1.10218 12 1.5V7.5C12 7.89782 11.842 8.27936 11.5607 8.56066C11.2794 8.84196 10.8978 9 10.5 9H4.5C4.10218 9 3.72064 8.84196 3.43934 8.56066C3.15804 8.27936 3 7.89782 3 7.5V1.5ZM4.5 0.75C4.30109 0.75 4.11032 0.829018 3.96967 0.96967C3.82902 1.11032 3.75 1.30109 3.75 1.5V7.5C3.75 7.69891 3.82902 7.88968 3.96967 8.03033C4.11032 8.17098 4.30109 8.25 4.5 8.25H10.5C10.6989 8.25 10.8897 8.17098 11.0303 8.03033C11.171 7.88968 11.25 7.69891 11.25 7.5V1.5C11.25 1.30109 11.171 1.11032 11.0303 0.96967C10.8897 0.829018 10.6989 0.75 10.5 0.75H4.5ZM1.5 3.75C1.30109 3.75 1.11032 3.82902 0.96967 3.96967C0.829018 4.11032 0.75 4.30109 0.75 4.5V10.5C0.75 10.6989 0.829018 10.8897 0.96967 11.0303C1.11032 11.171 1.30109 11.25 1.5 11.25H7.5C7.69891 11.25 7.88968 11.171 8.03033 11.0303C8.17098 10.8897 8.25 10.6989 8.25 10.5V9.75H9V10.5C9 10.8978 8.84196 11.2794 8.56066 11.5607C8.27936 11.842 7.89782 12 7.5 12H1.5C1.10218 12 0.720644 11.842 0.43934 11.5607C0.158035 11.2794 0 10.8978 0 10.5V4.5C0 4.10218 0.158035 3.72064 0.43934 3.43934C0.720644 3.15804 1.10218 3 1.5 3H2.25V3.75H1.5Z"
        fill={fill}
      />
    </svg>
  );
}
