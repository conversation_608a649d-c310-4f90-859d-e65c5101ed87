# BTAPP: CodeIgniter 4.2.1


## Requirements

1. PHP 8.1 (at least)
    > Instructions below are for PHP built-in web server (not for XAMPP setup), make sure that you are able to run PHP in command line and make sure the PHP used is what is set on environment variable. Add the PHP in environment variable--.

    Adding Environment Variable `path` in WINDOWS:
    * https://i.imgur.com/K3vCuFa.png
    * https://i.imgur.com/WGlGKXU.png


1. NodeJS v14.16.0 (at least)
1. NPM 6.14.11 (at least)
1. MySQL 10.4.10-MariaDB (at least)



## Installation (First Run Only)
1. Create database and use aipro_local as database name (you may use any name)

1. Create `.env` file from `env` file

    Make sure that the following Environment Variables have values:
    ```
    # ENVIRONMENT
    CI_ENVIRONMENT = development
    # APP
    SITE_ENVIRONMENT = Dev
    app.baseURL = 'http://localhost:9000'
    apiOpenAIURL = 'http://localhost:8000/v1/wp/authenticate/'
    chatbotURL = 'http://127.0.0.1:5173/'
    EXTERNAL_API_URL = 'https://test.api.ai-pro.org/'
    # AWS S3
    S3_ACCESS_KEY = ""
    S3_SECRET_KEY = ""
    S3_BUCKET_NAME = ""
    S3_VERSION = "2006-03-01"
    S3_REGION = "us-west-2"
    # DATABASE
    database.default.hostname = localhost
    database.default.database = aipro_local
    database.default.username = root
    database.default.password = password
    database.default.DBDriver = MySQLi
    database.default.DBPrefix = aiapp_
    # CSRF
    security.csrfProtection = 'session'
    security.tokenRandomize = true
    security.tokenName = 'surfToken'
    security.headerName = 'X-CSRF-TOKEN'
    security.cookieName = 'surfToken'
    # security.expires = 7200
    security.regenerate = true
    security.redirect = false
    security.samesite = 'Lax'
    # OPENAI
    OPENAI_API_KEY=
    OPENAI_RATE_LIMIT=3
    ```

### To use composer packages from GITHub, do the following, otherwise SKIP this:
1. Run `composer radBuild`

    > if you encountered an error relating to **github oauth token**, go to this [link](https://nono.ma/github-oauth-token-for-github-com-contains-invalid-characters-on-composer-install) and perform **THE BRUTE-FORCE FIX**


### To use composer packages from GITLab, do the following:
1. Run `composer install`



## Run (Succeeding Runs)


### To use composer packages from GITHub, do the following, otherwise SKIP this:
1. Run `composer radBuild`
1. Run `composer localServe`

### To use composer packages from GITLab, do the following:
1. Run `composer localBuild`
1. Run `composer localServe0`
    * To run under port 9001, run `composer localServe1` (make sure to update `app.baseURL` in `.env`)
    * To run under port 9002, run `composer localServe2` (make sure to update `app.baseURL` in `.env`)

## Set up DB

1. Run `php spark migrate`


# Debug

## Routes

To test isPRIVATE routes, manual add cookie `aiwp_logged_in`. Example value is `<EMAIL>|`

## Debug `btcomponents`

### CHATBOT client_v2

* You can directly debug and apply code changes in `vendor/codebay/btcomponents/CHATBOT/client_v2`. Do not forget to push changes.
    * `npm run dev` is working here

* To publish `vendor/codebay/btcomponents/CHATBOT/client_v2/dist` files to App `public` folder, on App `root` run `composer importChatBotV2`

    * access `${app.baseURL}/chatbot` to test