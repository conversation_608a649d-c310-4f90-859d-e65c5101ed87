{"name": "codebay/btapp", "description": "BTApp", "type": "project", "minimum-stability": "dev", "prefer-stable": true, "repositories": [{"url": "https://github.com/baytechph/btcore.git", "type": "vcs", "require-all": false}, {"url": "https://gitlab.baytech.ph/baytech/btcore.git", "type": "vcs", "require-all": false}], "require": {"btapp/btcore": "dev-ai_pro", "php": ">8.0", "codeigniter4/framework": "4.2.11", "mobiledetect/mobiledetectlib": "^2.8.41", "guzzlehttp/guzzle": "^6.0"}, "require-dev": {"fakerphp/faker": "^1.9", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": "^8.5"}, "suggest": {"ext-fileinfo": "Improves mime type detection for files"}, "autoload": {"psr-4": {"App\\": "app", "Config\\": "app/Config"}, "exclude-from-classmap": ["**/Database/Migrations/**"]}, "config": {"allow-plugins": {"php-http/discovery": false}}}