import type { IconProps } from "./IconProps";

export default function Icon({
  className,
  width = "14",
  height = "14",
  fill = "currentColor",
  size = null,
}: IconProps) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width={size || width}
      height={size || height}
      viewBox="0 0 19 18"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.25008 3.375C7.37231 3.37503 7.49119 3.41486 7.58876 3.48847C7.68633 3.56209 7.75726 3.66548 7.79083 3.783L8.40058 5.9175C8.53187 6.37719 8.77817 6.79582 9.11621 7.13387C9.45426 7.47192 9.87289 7.71821 10.3326 7.8495L12.4671 8.45925C12.5845 8.49289 12.6878 8.56386 12.7614 8.66142C12.8349 8.75898 12.8747 8.87783 12.8747 9C12.8747 9.12217 12.8349 9.24102 12.7614 9.33858C12.6878 9.43614 12.5845 9.50711 12.4671 9.54075L10.3326 10.1505C9.87289 10.2818 9.45426 10.5281 9.11621 10.8661C8.77817 11.2042 8.53187 11.6228 8.40058 12.0825L7.79083 14.217C7.75719 14.3344 7.68622 14.4377 7.58866 14.5113C7.4911 14.5848 7.37225 14.6246 7.25008 14.6246C7.12791 14.6246 7.00906 14.5848 6.9115 14.5113C6.81394 14.4377 6.74298 14.3344 6.70933 14.217L6.09958 12.0825C5.9683 11.6228 5.722 11.2042 5.38395 10.8661C5.04591 10.5281 4.62727 10.2818 4.16758 10.1505L2.03308 9.54075C1.91564 9.50711 1.81234 9.43614 1.7388 9.33858C1.66526 9.24102 1.62549 9.12217 1.62549 9C1.62549 8.87783 1.66526 8.75898 1.7388 8.66142C1.81234 8.56386 1.91564 8.49289 2.03308 8.45925L4.16758 7.8495C4.62727 7.71821 5.04591 7.47192 5.38395 7.13387C5.722 6.79582 5.9683 6.37719 6.09958 5.9175L6.70933 3.783C6.7429 3.66548 6.81384 3.56209 6.9114 3.48847C7.00897 3.41486 7.12786 3.37503 7.25008 3.375ZM14.0001 1.125C14.1256 1.12493 14.2475 1.16682 14.3464 1.24401C14.4453 1.32121 14.5156 1.42926 14.5461 1.551L14.7396 2.328C14.9166 3.033 15.4671 3.5835 16.1721 3.7605L16.9491 3.954C17.0711 3.98421 17.1794 4.05439 17.2568 4.15335C17.3343 4.25231 17.3763 4.37434 17.3763 4.5C17.3763 4.62566 17.3343 4.74769 17.2568 4.84665C17.1794 4.94561 17.0711 5.01579 16.9491 5.046L16.1721 5.2395C15.4671 5.4165 14.9166 5.967 14.7396 6.672L14.5461 7.449C14.5159 7.57097 14.4457 7.67931 14.3467 7.75675C14.2478 7.83419 14.1257 7.87626 14.0001 7.87626C13.8744 7.87626 13.7524 7.83419 13.6534 7.75675C13.5545 7.67931 13.4843 7.57097 13.4541 7.449L13.2606 6.672C13.174 6.32582 12.995 6.00967 12.7427 5.75736C12.4904 5.50504 12.1743 5.32604 11.8281 5.2395L11.0511 5.046C10.9291 5.01579 10.8208 4.94561 10.7433 4.84665C10.6659 4.74769 10.6238 4.62566 10.6238 4.5C10.6238 4.37434 10.6659 4.25231 10.7433 4.15335C10.8208 4.05439 10.9291 3.98421 11.0511 3.954L11.8281 3.7605C12.1743 3.67396 12.4904 3.49496 12.7427 3.24264C12.995 2.99033 13.174 2.67418 13.2606 2.328L13.4541 1.551C13.4845 1.42926 13.5548 1.32121 13.6538 1.24401C13.7527 1.16682 13.8746 1.12493 14.0001 1.125ZM12.8751 11.25C12.9932 11.2499 13.1084 11.2871 13.2042 11.3561C13.3001 11.4252 13.3718 11.5227 13.4091 11.6347L13.7046 12.522C13.8171 12.8572 14.0796 13.1212 14.4156 13.233L15.3028 13.5292C15.4146 13.5668 15.5117 13.6384 15.5805 13.7341C15.6493 13.8298 15.6863 13.9446 15.6863 14.0625C15.6863 14.1804 15.6493 14.2952 15.5805 14.3909C15.5117 14.4866 15.4146 14.5582 15.3028 14.5958L14.4156 14.892C14.0803 15.0045 13.8163 15.267 13.7046 15.603L13.4083 16.4902C13.3708 16.602 13.2992 16.6991 13.2035 16.7679C13.1078 16.8367 12.9929 16.8737 12.8751 16.8737C12.7572 16.8737 12.6424 16.8367 12.5467 16.7679C12.451 16.6991 12.3793 16.602 12.3418 16.4902L12.0456 15.603C11.9903 15.4375 11.8973 15.287 11.7739 15.1636C11.6505 15.0402 11.5001 14.9472 11.3346 14.892L10.4473 14.5958C10.3356 14.5582 10.2385 14.4866 10.1697 14.3909C10.1009 14.2952 10.0639 14.1804 10.0639 14.0625C10.0639 13.9446 10.1009 13.8298 10.1697 13.7341C10.2385 13.6384 10.3356 13.5668 10.4473 13.5292L11.3346 13.233C11.6698 13.1205 11.9338 12.858 12.0456 12.522L12.3418 11.6347C12.3791 11.5228 12.4507 11.4254 12.5464 11.3563C12.6421 11.2873 12.7571 11.2501 12.8751 11.25Z"
        fill={fill}
      />
    </svg>
  );
}
