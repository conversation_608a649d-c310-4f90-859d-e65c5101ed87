import logo from "../assets/images/logo.png";
import { copyText } from "../utils/copyText";
import type { Message } from "../types/message";

import DownloadIcon2 from "./icons/DownloadIcon2";
import CopyIcon from "./icons/CopyIcon";
import CheckMark from "./icons/CheckMark";
import Tooltip from "./Tooltip";
import { useState } from "react";

declare global {
  interface Window {
    btutil_buildExportModal: (onClickExportToPDF: () => void) => HTMLDivElement;
    generatePDF: (
      response: string | null | undefined,
      filename: string,
    ) => void;
  }
}

export default function AiResponse({ message }: { message: Message }) {
  const [isCopied, setIsCopied] = useState(false);

  const onClickExportButton = (content: string) => {
    const filename = "export-proper-english";
    const generatePDF = window.generatePDF;
    generatePDF(content, filename);
  };

  return (
    <div className="align-start mx-auto flex w-[733px] max-w-full gap-[4px] px-[30px] md:px-0">
      <div className="inline-flex h-[35px] w-[35px] items-center justify-center gap-2.5 rounded-[100px]">
        <img
          className="relative flex w-[27px] items-center justify-center"
          src={logo}
        />
      </div>
      <div
        className="flex w-full md:w-[623px] flex-col gap-[11px] py-[4px] pr-[53px] md:pr-0"
      >
        {message.generating !== true && (
          <div className="border-b border-[#E4E4E7] pb-[15px] dark:border-[#32363D]">
            Here's the corrected version of your sentence:
          </div>
        )}

        {message.generating ? (
          <div className="relative w-fit">
            <div className="linear-loader absolute"></div>
            <div>Analyzing grammar...</div>
          </div>
        ) : (
          <div
            className="w-[95%] font-[600] break-words"
            dangerouslySetInnerHTML={{ __html: message.content }}
          />
        )}

        {message.generating !== true && (
          <div className="icon-container flex gap-[9px]">
            <button
              className="group relative"
              onClick={() => {
                copyText(message.content.replace(/<br\s*\/?>/gi, "\n"));
                setIsCopied(true);

                setTimeout(() => {
                  setIsCopied(false);
                }, 3000);
              }}
            >
              {isCopied ? (
                <>
                  <Tooltip
                    tooltip="Copied to clipboard"
                    className="hidden group-hover:block"
                  />
                  <CheckMark />
                </>
              ) : (
                <>
                  <Tooltip
                    tooltip="Copy to clipboard"
                    className="hidden group-hover:block"
                  />
                  <CopyIcon size={17} className="cursor-pointer" />
                </>
              )}
            </button>

            <button
              className="group relative"
              onClick={() => onClickExportButton(message.content)}
            >
              <Tooltip
                tooltip="Export to PDF"
                className="hidden group-hover:block"
              />
              <DownloadIcon2 size={20} className="cursor-pointer dark:invert" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
