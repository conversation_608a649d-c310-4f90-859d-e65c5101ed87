// utils/scrollUp.ts

export function scrollUp(target?: string | HTMLElement | null) {
  let element: HTMLElement | null;

  if (!target) {
    // Scroll to top of the page
    window.scrollTo({ top: 0, behavior: "smooth" });
    return;
  }

  if (typeof target === "string") {
    element = document.querySelector<HTMLElement>(target);
  } else {
    element = target;
  }

  if (element) {
    element.scrollTo({ top: 0, behavior: "smooth" });
  }
}

export function scrollDown(target?: string | HTMLElement | null) {
  let element: HTMLElement | null;

  if (!target) {
    // Scroll to bottom of the page
    window.scrollTo({
      top: document.body.scrollHeight,
      behavior: "smooth",
    });
    return;
  }

  if (typeof target === "string") {
    element = document.querySelector<HTMLElement>(target);
  } else {
    element = target;
  }

  if (element) {
    element.scrollTo({
      top: element.scrollHeight,
      behavior: "smooth",
    });
  }
}

// utils/scrollToElement.ts

export function scrollToElement(
  target: string | HTMLElement | null,
  offset = 0,
  behavior: ScrollBehavior = "smooth",
) {
  let element: HTMLElement | null;

  if (typeof target === "string") {
    element = document.querySelector<HTMLElement>(target);
  } else {
    element = target;
  }

  if (element) {
    const top = element.getBoundingClientRect().top + window.scrollY + offset;
    window.scrollTo({
      top,
      behavior,
    });
  }
}
