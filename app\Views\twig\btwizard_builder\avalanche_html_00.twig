{% extends 'base_avalanche_00.twig' %}
{% block styles %}
  {{ parent() }}
  <link rel='stylesheet' href='https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css?ver={{ GLOBAL_DATA.BUILD }}' />
  <link rel='stylesheet' href='https://cdn.form.io/formiojs/formio.full.min.css?ver={{ GLOBAL_DATA.BUILD }}' />
  <link rel='stylesheet' href='{{ GLOBAL_DATA.BASE_URL }}/themes/avalanche/css/builder/main.css?ver={{ GLOBAL_DATA.BUILD }}'>
    <style>
      .component-edit-container {
          overflow: visible !important;
      }
      .btwizard-fillable-fields .builder-component {
        font-size: 12px;
        border: 1px dashed #0ed0ee;
        background-color: aliceblue;
        /* bottom-margin: 10px; */
      }
      .btwizard-fillable-fields .fieldset-body .drag-container {
        border: none;
      }
      .btwizard-fillable-fields .builder-component .form-group {
        margin: 5px;
      }

      #saveButton {
        /* line-height: 12px; */
        width: 140px;
        margin-top: 8px;
        margin-right: 70px;
        position:absolute;
        top:0;
        right:0;

        /* style="margin-right: 10px; margin-left: 10px;" */
      }
    </style>
  <link rel='stylesheet' href="{{ VIEW_DATA.app_assets }}styles/customFormio.css" />
{% endblock %}
{% block pre_scripts %}
  {{ parent() }}
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.min.js?ver={{ GLOBAL_DATA.BUILD }}"></script>
  <script src='https://cdn.form.io/formiojs/formio.full.min.js?ver={{ GLOBAL_DATA.BUILD }}'></script>
  <script type="text/javascript"  src="{{ VIEW_DATA.app_assets }}js/customFormio.js"></script>
  <script type='text/javascript'>

    var forceCache = {{ GLOBAL_DATA.FORCE_CACHE }};
    var form_data = localStorage.getItem('btwizard_formio_forms');
    var form_options = localStorage.getItem('btwizard_formio_options');
    //console.log(form_data);
    if (form_data == null || forceCache==0) {
      form_data = JSON.stringify({{ VIEW_DATA.btwizard_formio_forms | raw }});
      localStorage.setItem('btwizard_formio_forms', form_data);
      form_options = JSON.stringify({{ VIEW_DATA.btwizard_formio_options | raw }});
      localStorage.setItem('btwizard_formio_options', form_options);
    }
      // collapsible panels (section|page)
       let formData = JSON.parse(form_data);
       formData.components[0].columns.forEach((components)=>{
          components.components.forEach((component)=>{
              if(component.type == 'panel') {
                component.collapsible = true;
            }
          })
        });
        form_data = JSON.stringify(formData);
        localStorage.setItem('btwizard_formio_forms', form_data);
        var panelIds=[];
        window.addEventListener('click', (e) => {
          if (e.target.classList.contains('card-header')
            || e.target.classList.contains('card-title')
            || e.target.classList.contains('formio-collapse-icon')
          ){
            if (e.target.querySelector('.formio-collapse-icon')
              || e.target.classList.contains('formio-collapse-icon')
            ){
                let id;
                if(e.target.classList.contains('card-header')){
                  id = e.target.getAttribute('aria-controls');
                } else if (e.target.classList.contains('card-title')){
                  id = e.target.parentElement.getAttribute('aria-controls');
                } else if (e.target.classList.contains('formio-collapse-icon')){
                  id = e.target.parentElement.parentElement.getAttribute('aria-controls');
                }
                let body = document.getElementById(id);
                if(e.target.querySelector('.fa-minus-square-o')
                  || e.target.classList.contains('fa-minus-square-o')
                ){
                    body.classList.add('d-none');
                    panelIds.push(id);
                }
                if(e.target.querySelector('.fa-plus-square-o')
                  || e.target.classList.contains('fa-plus-square-o')
                ){
                    panelIds = panelIds.filter((panelId)=>{
                      return panelId != id;
                    });
                    body.classList.remove('d-none');
                }
            }
          }
        });


		window.onload = function () {

			Formio.builder(
				document.getElementById('builder'),
				JSON.parse(form_data) ,
				JSON.parse(form_options) ,
			).then(function (form) {
        expand_collapse(panelIds);
        removeSettingsButtonTitleForm();
        removeSettingsButtonCol3FieldSet();
        removeSettingsButton();
        form.on('editComponent', function (component) {
          //show loader
          document.getElementById("loader").style.display="flex";
          setTimeout(() => {
          expand_collapse(panelIds);
          let editor =  document.querySelector(".formio-dialog-content");
          editor.classList.add("custom-formio");
          //hide loader
           document.getElementById("loader").style.display="none";
          }, 800);
          setTimeout(function () {
              var fontFamily = document.querySelectorAll(".formio-form .formio-component-textarea .ck-editor .ck-toolbar  .ck-font-family-dropdown  .ck-list__item");
              fontFamily.forEach(function (font) {
                    if (font.innerText != "Default" &&
                      font.innerText != "Arial" &&
                      font.innerText != "Times New Roman"
                  ) {
                      font.style.display = "none";
                  }
              });
          }, 500);
          removeHelpButton();
          removeSettingsButtonTitleForm();
          removeSettingsButtonCol3FieldSet();
          removeSettingsButton();


          if(component.type == "number"){
              addExample();
          } else{
              exampleValue();
          }
          // for conditional
           addExamplejs();
          hideTabs(component.hidden_tabs , component.show_inputs , component.hidden_inputs);
          let form_index = 0;
          if(component.type && component.type == "content"){
            form_index = 1;
          }

            if (component.hidden_inputs && component.hidden_inputs.length > 0) {
                if (component.hidden_inputs && component.hidden_inputs.includes('label')) {
                    // if input label is hidden, change required to false and remove value
                    form.editForm.components[form_index].component.components[0].components[0].validate.required = false;
                    form.editForm._data.label = "";
                }
                setTimeout(() => {
                    showOrHideInputs(component.hidden_inputs, "hide");
                }, 500);
            } else if(component.show_inputs && component.show_inputs.length > 0) {
                if (component.show_inputs && component.show_inputs.includes('label')) {
                    // if input label is shown, change required to true
                    form.editForm.components[form_index].component.components[0].components[0].validate.required = true;
                } else{
                    // if input label is not shown, change required to false and remove value
                    form.editForm.components[form_index].component.components[0].components[0].validate.required = false;
                    form.editForm._data.label = "";
                }
                setTimeout(() => {
                    hideAllInputfields();
                    showOrHideInputs(component.show_inputs, "show");
                }, 500);
            }
            // change description field to input field
          if (component.descriptionField) {
            var description = document.querySelector(".formio-form .formio-component-description");
            hideDescriptionField();
            const randomNumber = Math.floor(Math.random() * 999) + 1;
            let value = `[FIELD ${randomNumber}]`;
              if(component.isEditing){
               value = component.description;
              }
            // set the value
            desciptionValue = value ;  // desciptionValue is global variable
            const placeholder = component?.descriptionField?.placeholder || "";
            createDescriptionInput(placeholder,value,component?.descriptionField?.required);
            setTimeout(() =>{
               // set description value and preview value
                form.editForm._data.description = value;
                let description_preview = document.querySelector(".component-preview .formio-form .formio-component .form-text.text-muted");
                if (!description_preview && !document.getElementById(description_preview?.id)) {
                  createDescriptionPreview(value);
                } else {
                document.getElementById(description_preview.id).innerHTML =  value;
                }
                let descriptionInput =  document.getElementById("descriptionField");
                descriptionInput.addEventListener("keyup", function (event) {
                    form.editForm._data.description = event.target.value;
                    descriptionValue = event.target.value;
                });
                 form.editForm._data.isEditing = true;
            }, 500);
          }

        })
				form.on("change", function (e) {
          expand_collapse(panelIds,true);
          removeSettingsButtonTitleForm();
          removeSettingsButtonCol3FieldSet();
          removeSettingsButton();
          localStorage.setItem('btwizard_formio_forms', JSON.stringify(e));
          form_data = JSON.stringify(e);
				});
			});


      Formio.createForm(document.getElementById('formio'),
        {"components":[

          {
            "label": "Container",
            "tableView": false,
            "key": "container",
            "type": "container",
            "input": true,
            "components": [
              {
                "label": "Wizard Forms",
                "action": "custom",
                "showValidations": false,
                "theme": "secondary",
                "size": "sm",
                "leftIcon": "fa fa-arrow-left",
                "customClass": "warning-white",
                "tableView": false,
                "key": "back",
                "attributes": {
                  "style": "color:#000; background-color:#e9ecef"
                },
                "type": "button",
                "custom": "window.location.href = '{{ GLOBAL_DATA.BASE_URL }}/admin/forms-library';",
                "input": true
              },
              {
                "title": "FORM PROPERTIES",
                "collapsible": false,
                "key": "viewDataFormTitle",
                "type": "panel",
                "label": "Panel",
                "input": false,
                "tableView": false,
                "components": [
                  {
                    "content": "<u>Form ID</u>:<br>{{ VIEW_DATA.form_pid }}",
                    "key": "formName3",
                    "type": "htmlelement",
                    "input": false,
                    "tableView": false,
                    "label": "HTML"
                  },
                  {
                    "content": "<u>Form Name</u>:<br>{{ VIEW_DATA.form_title }}",
                    "key": "formName3",
                    "type": "htmlelement",
                    "input": false,
                    "tableView": false,
                    "label": "HTML"
                  },
                  {
                  "content": "<u>Slug</u>:<br>{{ VIEW_DATA.slug }}",
                  "key": "formName3",
                  "type": "htmlelement",
                  "input": false,
                  "tableView": false,
                  "label": "HTML"
                  }
                ]
              },
              {
                "title": "Actions",
                "collapsible": false,
                "key": "viewDataFormTitle1",
                "type": "panel",
                "label": "Panel",
                "input": false,
                "tableView": false,
                "components": [
                  // {
                  //   "label": "Publish",
                  //   "tableView": false,
                  //   "key": "publish",
                  //   "type": "checkbox",
                  //   "input": true,
                  //   "defaultValue": false
                  // },
                  {
                    "label": "Save",
                    "action": "custom",
                    "showValidations": false,
                    "theme": "success",
                    "block": true,
                    "disableOnInvalid": true,
                    "tableView": false,
                    "key": "submit",
                    "conditional": {
                      "show": false,
                      "when": "container.publish",
                      "eq": "true"
                    },
                    "type": "button",
                    "custom": "save(1);",
                    "input": true
                  },
                  {
                    "label": "Save and Publish",
                    "action": "custom",
                    "showValidations": false,
                    "theme": "warning",
                    "block": true,
                    "disableOnInvalid": true,
                    "tableView": false,
                    "key": "submit1",
                    "conditional": {
                      "show": true,
                      "when": "container.publish",
                      "eq": "true"
                    },
                    "type": "button",
                    "custom": "save(2);",
                    "input": true
                  },
                  {
                    "label": "Generate Fillable Fields",
                    "action": "custom",
                    "showValidations": false,
                    "theme": "secondary",
                    "size": "sm",
                    "block": true,
                    "disableOnInvalid": true,
                    "tableView": false,
                    "key": "submit2",
                    "type": "button",
                    "custom": "save(3);",
                    "input": true
                  }
                ]
              },
              {
                "title": "Editor Links",
                "collapsible": false,
                "key": "wizardForms",
                "type": "panel",
                "label": "Panel",
                "input": false,
                "tableView": false,
                "components": [
                  {
                    "label": "HTML",
                    "content": "<ul class=\"fa-ul\"><li><i class=\"fa fa-file-text-o\"></i><a href=\"{{ GLOBAL_DATA.MAIN_APP_URL }}wizard/pre-fill/{{ VIEW_DATA.form_pid }}?wz=ltr2pdf\" target=\"_blank\">  ?wz=ltr2pdf</a>  </li>  <li><i class=\"fa fa-file-text-o\"></i><a href=\"{{ GLOBAL_DATA.MAIN_APP_URL }}wizard/pre-fill/{{ VIEW_DATA.form_pid }}?wz=pdf2pdf\" target=\"_blank\">  ?wz=pdf2pdf</a>  </li>  <li><i class=\"fa fa-file-text-o\"></i><a href=\"{{ GLOBAL_DATA.MAIN_APP_URL }}document/editor/{{ VIEW_DATA.form_pid }}?editor=14\" target=\"_blank\">  ?editor=14</a>  </li>  <li><i class=\"fa fa-file-text-o\"></i><a href=\"{{ GLOBAL_DATA.MAIN_APP_URL }}editor/form/{{ VIEW_DATA.form_pid }}?editor=07\" target=\"_blank\">  ?editor=07</a>  </li><li><i class=\"fa fa-file-text-o\"></i><a href=\"{{ GLOBAL_DATA.MAIN_APP_URL }}document/editor/{{ VIEW_DATA.form_pid }}?editor=03\" target=\"_blank\">  ?editor=03</a>  </li></ul>",
                    "refreshOnChange": false,
                    "key": "html",
                    "type": "htmlelement",
                    "input": false,
                    "tableView": false
                  }
                ]
              }
            ]
          }

        ]},
        {}
      ).then(function (form) {
        form.on("change", function (e) {
        });
      });
		};
	</script>
{% endblock %}
{% block body_header %}
  <div id="loader">
    <div id="circle"></div>
  </div>
  <header class="app-header fixed-top">
    <div class="app-header-inner">
      <div class="container-fluid py-2">
        <div class="app-header-content">
          <div class="row justify-content-between align-items-center">
            <div class="col-auto">
              <a id="sidepanel-toggler" class="sidepanel-toggler d-inline-block d-xl-none" href="#">
                <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewbox="0 0 30 30" role="img">
                  <title>
                    Menu
                  </title>
                  <path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path>
                </svg>
              </a>
            </div>
            {# <button id="saveButton" onclick="javascript:save(1);"  class="btn btn-success">
              <i class="fa fa-save"> </i> Save
            </button> #}
          </div>
        </div>
      </div>
    </div>
    <div id="app-sidepanel" class="app-sidepanel sidepanel-hidden">
      <div id="sidepanel-drop" class="sidepanel-drop"></div>
      <div class="sidepanel-inner d-flex flex-column">
        <a href="#" id="sidepanel-close" class="sidepanel-close d-xl-none"></a>
        <div style="padding: 10px">
          <div id='formio'></div>
        </div>
      </div>
    </div>
  </header>


  {% endblock %}
  {% block body_modal %}
    {{ parent() }}
  {% endblock %}
  {% block body_content %}
    <div style="padding: 20px">
      <div id='builder'></div>
    </div>
  {% endblock %}
  {% block body_footer %}
    {{ parent() }}
  {% endblock %}
  {% block post_scripts %}
    {{ parent() }}
    <script src="{{ VIEW_DATA.theme_assets }}js/app.js?ver={{ GLOBAL_DATA.BUILD }}"></script>
    <script type='text/javascript'>

    var isClicked = 0;




    function save(saveOption=0) {
      if (isClicked) {
        return;
      }
      isClicked = 1;
      console.log('saving as draft...');

      toastr.options = {
        "closeButton": true,
        "debug": false,
        "newestOnTop": true,
        "progressBar": true,
        "positionClass": "toast-top-center",
        "preventDuplicates": true,
        "onclick": null,
        "showDuration": "300",
        "hideDuration": "1000",
        "timeOut": "10000",
        "extendedTimeOut": "2000",
        "showEasing": "swing",
        "hideEasing": "linear",
        "showMethod": "fadeIn",
        "hideMethod": "fadeOut"
      }

      if (!saveOption) {
        toastr.info("Something went wrong. Please try agaiu later", "Failed!");
        return;
      }

      var form_name = "{{ VIEW_DATA.form_title }}";
      var form_pid = "{{ VIEW_DATA.form_pid }}";
      var slug = "{{ VIEW_DATA.slug }}";
      var url = '{{ GLOBAL_DATA.BASE_URL }}/btwizard/crud/update';
      var formData = new FormData();
      formData.append('data', form_data);
      formData.append('form_name', form_name);
      formData.append('form_pid', form_pid);
      formData.append('saveOption', saveOption);
      formData.append('slug', slug);

      if (saveOption>1) {
        toastr.options.timeOut = 10000;
        toastr.info("Saving first...", "");
      } else {
        toastr.info("Saving...", "");
      }

      fetch(url, { method: 'POST', body: formData })
        .then(function (response) {
          return response.json();
        })
        .then(function (data) {
          //console.log(data);
          toastr.options.progressBar = false;
          toastr.options.timeOut = 2000;

          if (data.status) {

            console.log('saved!');

            if (saveOption==1) {
              toastr.clear();
              toastr.success(data.response_success, "Success");
              isClicked = 0;
            }

            if (saveOption==2) {
              setTimeout(() => {
                toastr.options.timeOut = 0;
                toastr.options.extendedTimeOut = 0;
                toastr.options.tapToDismiss = true;
                toastr.warning(data.response_warning, "");
              }, "1000");
              console.log('published!');
              isClicked = 0;
            }

            if (saveOption==3) {
              toastr.clear();
              toastr.options.progressBar = true;
              toastr.options.timeOut = 20000;
              console.log('timeout: 20000');
              // toastr.options.extendedTimeOut = 3000;
              // toastr.options.showDuration = 3000;
              // toastr.options.hideDuration = 3000;
              toastr.warning("Generating Fillable Fields...", "");
              generateFillableFields();
            }

          } else {
            toastr.error(data.response_error, "Error");
            console.log('error!');
          }
        }
      );
    }


    function generateFillableFields() {
      // save(3);
      if (!isClicked) {
        return;
      }
      console.log(isClicked);

      var url = '{{ GLOBAL_DATA.BASE_URL }}/btwizard/DataConverter/process/{{ VIEW_DATA.slug }}/PdfrunfillablefieldToFormio';
      // var formData = new FormData();
      let response = fetch(url); //TODO: is this still needed?

      fetch(url)
        .then(response => {
            // handle the response
          return response.json();
        })
        .then((data) => {
          console.log(data);
          isClicked = 0;
          toastr.clear();
          location.reload();
        })
        .catch(error => {
            // handle the error
        });


      // fetch(url, { method: 'GET', formData })
      //   .then(function (response) {
      //     return response.json();
      //   })
      //   .then(function (data) {
      //     console.log(data);
      //     // toastr.options.progressBar = false;
      //     // toastr.options.timeOut = 2000;

      //     if (data.status) {
      //       // toastr.success(data.response_success, "Success");
      //       console.log('generated!');


      //     } else {
      //       toastr.error(data.response_error, "Error");
      //       console.log('error!');
      //     }
      //   }
      // );

    }

  </script>
  {% endblock %}
