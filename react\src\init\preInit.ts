import { setCookie, getCookie } from "../utils/cookie";

const setFlags = () => {
  const allowedFlags = ["mode", "reg_google", "reg_apple"];
  const params = new URLSearchParams(window.location.search);
  params.forEach((value, key) => {
    if (allowedFlags.includes(key)) {
      console.log("key", key, "value", value);
      setCookie(key, value, { path: "/", domain: ".ai-pro.org" });
    }
  });
};

const defaultFlags: Record<string, string> = {
  reg_google: "on",
  reg_apple: "on",
};

const setDefaultFlags = () => {
  Object.entries(defaultFlags).forEach(([key, value]) => {
    if (!getCookie(key)) {
      setCookie(key, value, {
        path: "/",
        domain: ".ai-pro.org",
      });
    }
  });
};

const forceFlags: Record<string, string> = {
  ppg: "97",
  // app: "pro",
  kt8typtb: "echo",
};

const setForceFlags = () => {
  Object.entries(forceFlags).forEach(([key, value]) => {
    setCookie(key, value, {
      path: "/",
      domain: ".ai-pro.org",
    });
  });
};

export function preInit() {
  setFlags();
  setDefaultFlags();
  setForceFlags();
}
