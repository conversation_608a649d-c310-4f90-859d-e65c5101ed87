# ENVIRONMENT
CI_ENVIRONMENT = development
# APP
SITE_ENVIRONMENT = Dev
app.baseURL = 'http://localhost:9000'
apiOpenAIURL = 'http://localhost:8000/v1/wp/authenticate/'
chatbotURL = 'http://127.0.0.1:5173/'
EXTERNAL_API_URL = 'https://test.api.ai-pro.org/'
# AWS S3
S3_ACCESS_KEY = ""
S3_SECRET_KEY = ""
S3_BUCKET_NAME = ""
S3_VERSION = "2006-03-01"
S3_REGION = "us-west-2"
# DATABASE
database.default.hostname = localhost
database.default.database = aipro_local
database.default.username = root
database.default.password = password
database.default.DBDriver = MySQLi
database.default.DBPrefix = aiapp_
# CSRF
security.csrfProtection = 'session'
security.tokenRandomize = true
security.tokenName = 'surfToken'
security.headerName = 'X-CSRF-TOKEN'
security.cookieName = 'surfToken'
# security.expires = 7200
security.regenerate = true
security.redirect = false
security.samesite = 'Lax'
# OPENAI
OPENAI_API_KEY=
OPENAI_RATE_LIMIT=3

# OPENSOURCE_ENDPOINT="https://openchat.llm.ai-pro.org/v1" # openchat_3.5-GPTQ
# OPENSOURCE_ENDPOINT="https://zephyr.llm.ai-pro.org/v1" # zephyr-7B-beta-GPTQ