{#
Structure based from: https://developer.mozilla.org/en-US/docs/Learn/HTML/Howto/Author_fast-loading_HTML_pages

Twig:
https://twig.symfony.com/doc/3.x/templates.html#template-inheritance
#}

<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta name="color-scheme" content="dark light">

		{% for meta in VIEW_DATA.meta_data %}
			<meta name="{{ meta.name }}" content="{{ meta.content }}">
		{% endfor %}

		<title>{{ VIEW_DATA.page_title }}</title>

		{% block styles %}{% endblock %}

		{% block pre_scripts %}{% endblock %}
	</head>


	<body>
		{% block body_header %}{% endblock %}

		{% block body_content %}{% endblock %}

		{% block body_modal %}{% endblock %}

		{% block body_footer %}{% endblock %}

		{% block post_scripts %}{% endblock %}
	</body>

</html>


