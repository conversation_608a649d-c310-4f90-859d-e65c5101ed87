var root_path = '../../';

// require('dotenv').config({ path: root_path+'.env' });

const { series, parallel } = require('gulp');
const { src, dest } = require('gulp');

var isDebug = 0;

var cleanCSS = require('gulp-clean-css');
var concat = require('gulp-concat');
var del = require('del');
var fs = require('fs');
var minifyImage = require('gulp-imagemin');
var removeEmptyLines = require('gulp-remove-empty-lines');
var rename = require('gulp-rename');
var sourcemaps = require('gulp-sourcemaps');
var paths = {};
var asset_filetypes = 'png|jpg|jpeg|gif|svg|eot|ttf|woff';
var jsonminify = require('gulp-jsonminify');


if (isDebug) {
  console.log(process.env);
}

var paths = {
  formfields_json: {
    src: [
      root_path+'writable/btlibrary_forms/_raw/**/*.json',
    ],
    dest: root_path+'writable/btlibrary_forms/process/btlibrary/published/'
  },
  ltr2pdf_html: {
    src: [
      root_path+'writable/btlibrary_forms/_raw/**/*.html',
    ],
    dest: root_path+'writable/btlibrary_forms/process/btlibrary/published/'
  }
};


function clean() {
  return del([
    './writable/btlibrary_forms/process/btlibrary/published/**'
  ]);
}


function process_formfields_json() {
  return src(paths.formfields_json.src)
    .pipe(removeEmptyLines({
      removeSpaces: true
    }))
    .pipe(jsonminify())
    .pipe(rename({ extname: '.data' }))
    .pipe(dest(paths.formfields_json.dest));
}


function process_ltr2pdf_html() {
  return src(paths.ltr2pdf_html.src)
    .pipe(removeEmptyLines({
      removeSpaces: true
    }))
    .pipe(rename({ extname: '.data' }))
    .pipe(dest(paths.ltr2pdf_html.dest));
}


function replicate_form() {
  src(root_path+'writable/btlibrary_forms/process/btlibrary/published/mortgage-deed/*.*')
    .pipe(dest(root_path+'writable/btlibrary_forms/process/btlibrary/published/mortgage-deed-form/'));

  return src(root_path+'writable/btlibrary_forms/process/btlibrary/published/master-form/*.*')
    .pipe(dest(root_path+'writable/btlibrary_forms/process/btlibrary/published/master-forms/'));
}

exports.build = series(
  clean,
  parallel(process_formfields_json,process_ltr2pdf_html),
  replicate_form
);
