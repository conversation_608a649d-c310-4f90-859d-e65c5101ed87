#!/usr/bin/env php
<?php

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

/*
 * --------------------------------------------------------------------
 * CodeIgniter command-line tools
 * --------------------------------------------------------------------
 * The main entry point into the CLI system and allows you to run
 * commands and perform maintenance on your application.
 *
 * Because CodeIgniter can handle CLI requests as just another web request
 * this class mainly acts as a passthru to the framework itself.
 */

// Refuse to run when called from php-cgi
if (strpos(PHP_SAPI, 'cgi') === 0) {
    exit("The cli tool is not supported when running php-cgi. It needs php-cli to function!\n\n");
}

// Check PHP version.
$minPhpVersion = '7.4'; // If you update this, don't forget to update `public/index.php`.
if (version_compare(PHP_VERSION, $minPhpVersion, '<')) {
    $message = sprintf(
        'Your PHP version must be %s or higher to run CodeIgniter. Current version: %s',
        $minPhpVersion,
        PHP_VERSION
    );

    exit($message);
}

// We want errors to be shown when using it from the CLI.
error_reporting(-1);
ini_set('display_errors', '1');

/**
 * @var bool
 *
 * @deprecated No longer in use. `CodeIgniter` has `$context` property.
 */
define('SPARKED', true);

// Path to the front controller
define('FCPATH', __DIR__ . DIRECTORY_SEPARATOR . 'public' . DIRECTORY_SEPARATOR);

// Ensure the current directory is pointing to the front controller's directory
chdir(FCPATH);

/*
 *---------------------------------------------------------------
 * BOOTSTRAP THE APPLICATION
 *---------------------------------------------------------------
 * This process sets up the path constants, loads and registers
 * our autoloader, along with Composer's, loads our constants
 * and fires up an environment-specific bootstrapping.
 */

// Load our paths config file
// This is the line that might need to be changed, depending on your folder structure.
require FCPATH . '../app/Config/Paths.php';
// ^^^ Change this line if you move your application folder

$paths = new Config\Paths();

// Location of the framework bootstrap file.
require rtrim($paths->systemDirectory, '\\/ ') . DIRECTORY_SEPARATOR . 'bootstrap.php';

// Load environment settings from .env files into $_SERVER and $_ENV
require_once SYSTEMPATH . 'Config/DotEnv.php';
(new CodeIgniter\Config\DotEnv(ROOTPATH))->load();

// Grab our CodeIgniter
$app = Config\Services::codeigniter();
$app->initialize();

// Grab our Console
$console = new CodeIgniter\CLI\Console();

// Show basic information before we do anything else.
if (is_int($suppress = array_search('--no-header', $_SERVER['argv'], true))) {
    unset($_SERVER['argv'][$suppress]); // @codeCoverageIgnore
    $suppress = true;
}

$console->showHeader($suppress);

// fire off the command in the main framework.
$exit = $console->run();

exit(is_int($exit) ? $exit : EXIT_SUCCESS);
