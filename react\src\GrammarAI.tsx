import Header from "./components/Header";
import GrammarAIBrand from "./components/GrammarAIBrand";

import { useState, useEffect } from "react";
import { scrollDown } from "./utils/scroll";
import { cn } from "./utils/cn";
import Messages from "./components/Messages";
import Input from "./components/Input";
import type { Message } from "./types/message";
import { ToastContainer, toast, Bounce } from "react-toastify";
import PricingPopup from "./components/PricingPopup.tsx";
import LimitedUsageModal from "./components/LimitedUsageModal.tsx";
function GrammarAI() {
  const [url, setUrl] = useState("");
  const [slug, setSlug] = useState("");
  const [surfToken, setSurfToken] = useState("");

  useEffect(() => {
    setUrl(window._grammar_ai_url);
    setSlug(window._grammar_ai_slug);
    setSurfToken(window._grammar_ai_surfToken);
  }, []);

  const [isGenerating, setIsGenerating] = useState(false);

  const [messages, setMessages] = useState<Message[]>([
    {
      role: "user",
      content:
        "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Qui distinctio explicabo modi, repellendus consequatur architecto fugiat soluta labore laudantium dolore ea. Vitae recusandae corrupti doloremque explicabo qui! Dolores in incidunt nesciunt ex dolor repellendus fugit dolorem alias tempore sit aspernatur, commodi officia distinctio repudiandae pariatur perspiciatis magni sapiente maiores? Provident nulla, temporibus tenetur quam voluptatum asperiores similique molestiae suscipit fugiat numquam debitis reprehenderit aut. Sed mollitia dignissimos quia laborum, hic odit corporis omnis commodi maiores totam quidem expedita voluptates ipsum, modi repellendus delectus distinctio inventore et, reiciendis odio porro minus iure fugiat? Quas similique laboriosam at molestias praesentium incidunt consequuntur.",
      id: new Date().getTime().toString(),
    },
    {
      role: "assistant",
      content:
        "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Qui distinctio explicabo modi, repellendus consequatur architecto fugiat soluta labore laudantium dolore ea. Vitae recusandae corrupti doloremque explicabo qui! Dolores in incidunt nesciunt ex dolor repellendus fugit dolorem alias tempore sit aspernatur, commodi officia distinctio repudiandae pariatur perspiciatis magni sapiente maiores? Provident nulla, temporibus tenetur quam voluptatum asperiores similique molestiae suscipit fugiat numquam debitis reprehenderit aut. Sed mollitia dignissimos quia laborum, hic odit corporis omnis commodi maiores totam quidem expedita voluptates ipsum, modi repellendus delectus distinctio inventore et, reiciendis odio porro minus iure fugiat? Quas similique laboriosam at molestias praesentium incidunt consequuntur.",
      generating: false,
      id: new Date().getTime().toString(),
    },
        {
      role: "user",
      content:
        "test",
      id: new Date().getTime().toString(),
    },
    {
      role: "assistant",
      content:
        "LoremipsumdolorsitametconsecteturadipisicingelitQuidistinctioexplicabomodirepellendusconsequaturarchitectofugiatsolutalaborelaudantiumdoloreeaVitaerecusandaecorruptidoloremqueexplicaboquiDoloresininciduntnesciuntexdolorrepellendusfugitdoloremaliastemporesitaspernaturcommodiofficiadistinctiorepudiandaepariaturperspiciatismagnisapientemaioreProvidentnullatemporibusteneturquamvoluptatumasperioressimiliquemolestiaesuscipitfugiatnumquamdebitisreprehenderitautSedmollitiadignissimosquialaborumhicoditcorporisomniscommodimaiorestotamquidemexpeditavoluptatesipsummodirepellendusdelectusdistinctioinventoreetreiciendisodioporrominusiurefugiatQuassimiliquelaboriosamatmolestiaspraesentiuminciduntconsequuntur",
      generating: false,
      id: new Date().getTime().toString(),
    },
  ]);
  const [showPricing, setShowPricing] = useState(false);
  const [showLimitedUsageModal, setShowLimitedUsageModal] = useState(false);
  const [desc, setDesc] = useState("");
  const isParagraph = (input: string) => {
    const lines = input.split(/\r?\n/);
    const words = lines[0].split(/\s+/);
    return lines.length > 1 || words.length > 1 ? "true" : "false";
  };

  const handleGenerate = async (prompt: string) => {
    setIsGenerating(true);

    const userMessage = {
      id: new Date().getTime().toString(),
      role: "user",
      content: prompt,
    };
    const typingIndicator = {
      id: new Date().getTime().toString(),
      role: "assistant",
      content: "Analyzing grammar...",
      generating: true,
    };
    setMessages((prev) => [...prev, userMessage, typingIndicator]);

    // await new Promise((resolve) => setTimeout(resolve, 300));

    const _isParagraph = isParagraph(prompt);

    const formData = new FormData();
    formData.append("prompt", prompt);
    formData.append("slug", slug);
    formData.append("surfToken", surfToken);
    formData.append("isParagraph", _isParagraph);

    try {
      const response = await fetch(url, {
        method: "POST",
        body: formData,
      });

      const output = await response.json();

      if (output.status === "invalid") {
        // btutil_modalRegisterUpgrade();
        const btutil_modalRegisterUpgrade = window.btutil_modalRegisterUpgrade;
        btutil_modalRegisterUpgrade();
        return;
      } else if (output.status === "max-tokens") {
        if (output.ent_member && output.ent_member == "yes") {
          const btutil_modalMaxTokenUpgradeEntMembers =
            window.btutil_modalMaxTokenUpgradeEntMembers;
          btutil_modalMaxTokenUpgradeEntMembers();
          return;
        }
        const btutil_modalMaxTokenUpgrade = window.btutil_modalMaxTokenUpgrade;
        btutil_modalMaxTokenUpgrade();
      }
      if (output.success == 2) {
        setDesc(output.modal.desc);
        setShowLimitedUsageModal(true);
        return;
      }
      if (output.success) {
        setMessages((prevMessages) => [
          ...prevMessages.slice(0, -1),
          {
            id: new Date().getTime().toString(),
            role: "assistant",
            content: output.data,
          },
        ]);
      } else {
        setMessages((prevMessages) => [
          ...prevMessages.slice(0, -1),
          {
            id: new Date().getTime().toString(),
            role: "assistant",
            content: "Error",
          },
        ]);
        const reactElement = (
          <div dangerouslySetInnerHTML={{ __html: output.message }} />
        );
        toast.error(reactElement, {
          position: "top-center",
          autoClose: false,
          hideProgressBar: true,
          closeOnClick: true,
          pauseOnHover: false,
          draggable: false,
          progress: undefined,
          theme: "colored",
          transition: Bounce,
        });

        setIsGenerating(false);
        return;
      }
    } catch (error) {
      console.error("Fetch error:", error);
    }

    setIsGenerating(false);

    //trigger when user clicks Generate Proper English button
    //TrustPilot logic change
    const TPLogicRun = window.TPLogicRun;
    console.log("TPLogicRun", typeof TPLogicRun);
    if (typeof TPLogicRun === "function") {
      TPLogicRun();
    } else {
      console.error("TrustPilot TPLogicRun function failed to execute");
    }
  };

  useEffect(() => {
    scrollDown(".scroll-1");
  }, [messages]);

  return (
    <div className="flex h-[100dvh] max-h-[100dvh] min-h-[100dvh] flex-col overflow-hidden">
      <Header
        appName="Grammar AI"
        app="pro"
        setShowPricing={setShowPricing}
        className="dark:bg-black-100 absolute top-0 z-10 w-full bg-white"
      />
      <div
        className={cn(
          "mx-auto flex h-full max-h-full w-full flex-col items-center gap-[24px]",
          messages.length === 0
            ? "justify-center pb-[85px]"
            : "justify-between",
        )}
      >
        {messages.length === 0 ? (
          <GrammarAIBrand />
        ) : (
          <Messages messages={messages} />
        )}
        <Input handleGenerate={handleGenerate} isGenerating={isGenerating} />
      </div>
      <ToastContainer />

      <PricingPopup
        isVisible={showPricing}
        setIsVisible={setShowPricing}
        appName="Grammar AI"
        app="pro"
      ></PricingPopup>

      <LimitedUsageModal
        show={showLimitedUsageModal}
        setShowPricing={setShowPricing}
        desc={desc}
        appName={"Grammar AI"}
        app="pro"
      />
    </div>
  );
}
export default GrammarAI;
