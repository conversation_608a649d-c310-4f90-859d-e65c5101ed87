<?php

namespace App\Controllers\ArtificialIntelligence;

use App\Controllers\ArtificialIntelligence\_BaseArtificialIntelligence as AIBase;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;

/**
 *  Stable Diffusio API --> https://avatar-now.com/sdapi/v1/
 */
class ApiV2 extends AIBase
{
    //---------------------------------------------------------------------------------------------
    //  variables
    //---------------------------------------------------------------------------------------------

    protected $app;
    protected $appClass;

    //---------------------------------------------------------------------------------------------
    //  public
    //---------------------------------------------------------------------------------------------

    public function __construct() {
        parent::__construct();
    }

    public function v2($module = '') {
        $res = $this->appResponse;

        $resDeniedAccess = [
            'success' => 0,
            'message' => 'Access denied.',
        ];
        $req = $this->request->getPost();
        $reqSlug = $req['slug'];

        if (!isset($reqSlug) || empty($reqSlug) ){
            return $this->response->setJSON($resDeniedAccess);
        }

        $reqSurfToken = $req['surfToken'];
        if (!isset($reqSurfToken) || empty($reqSurfToken) ){
            return $this->response->setJSON($resDeniedAccess);
        }
        $this->sessionName = $reqSlug . '_' . $reqSurfToken;

        $allowAccess = false;

        switch ($reqSlug) {
            case 'create-art':
            case 'dream-photo':
            case 'text-to-image':
                $allowAccess = true;
                break;
        }

        if (!$allowAccess) {
            return $this->response->setJSON($resDeniedAccess);
        }

        // -------------------------------------
        // FILTER: authenticateProtectedAccess
        // -------------------------------------

        if(!$this->authenticatePrivateAccess()) {
            switch ($reqSlug) {
                case 'text-to-image':
                    $resRestrictions = $this->filterRestrictions($reqSlug, $req);
                    if ($resRestrictions) {
                        $dreamphotourl = getenv('DREAMPHOTO_URL') ? getenv('DREAMPHOTO_URL') : 'https://app.ai-pro.org/create-art';
                        btflag_set('app', 'basic', ['domain'=>'.ai-pro.org']);
                        btflag_set('appurl', $dreamphotourl, ['domain'=>'.ai-pro.org']);
                        $userEmail = btflag_cookie('aiwp_logged_in', 'unknown|');
                        $start_url = getenv('START_URL') ? getenv('START_URL') : START_URL;
                        $ctaModal_logic = btflag_cookie('regRedirectWP', '0') === '1' ? WP_URL : $start_url;
                        $tool_count = $this->getToolCount();
                        $title = 'Switch to PRO to Continue Using Text to Image';
                        $redirectURL = $start_url . '/pricing';
                        $register_url = '/register';
                        $r_flow = false;
                        switch(btflag_cookie('r_flow', '')) {
                            case 'chatpro':
                            case 'dreamphoto':
                            case 'chatpdf':
                              $r_flow = true;
                              break;
                        }

                        if(btflag_cookie('regRedirectWP', '0') === '1' || $r_flow){
													$getURL = $this->getWPRegisterUrl();
													$register_url = $getURL ? $getURL : $register_url;
                        }

                        if($userEmail === 'unknown|') {
                            $redirectURL = $ctaModal_logic . $register_url;
                        }

                        if(btflag(FLOW, null) === 'basilisk-02') {
                            $redirectURL = $start_url . '/splash';
                        }

												if($r_flow) {
													$redirectURL = WP_URL . $register_url;
												}

                        return $this->response->setJSON([
                            'success' => 2,
                            'message' => 'Restricted.',
                            'data' => $title,
                            'modal' => [
                                'title' => $title,
                                'subTitle' => 'Enjoying the trial version of Text to Image so far?',
                                'desc' => 'Switch now to PRO and get access to <span>'.$tool_count.'</span> different creativity and productivity AI tools.',
                                'ctaLabel' => 'CONTINUE',
                                'redirectUrl' => $redirectURL
                            ]
                        ]);
                    }
            }
        }
        log_message('debug', json_encode($req));

        switch ($module) {
            case 'test':
            case 'create-art':
                $res = $this->textToImage();
                break;
            case 'text-to-image':
                $res = $this->textToImage();
                break;
            default:
        }

        return $this->response->setJSON($res);
    }

    public function textToImage() {
        $res = $this->appResponse;
        $req = $this->request->getPost();

        log_message("debug", "texttoImage");
        log_message("debug", json_encode($req));

        if (!isset($req['prompt'])) {
            return $res;
        }

        $userPrompt = json_decode($req['prompt'], true);
        $otherPrompt = "mdjrny-v4 style ";
        $hasNegativePrompt = !empty($userPrompt['negative']);

        $slug = $req['slug'];

        $client = new Client();
        $params['headers'] = ['Content-Type' => 'application/json'];
        $params['json'] = [
            'prompt' => $hasNegativePrompt ? $userPrompt['positive'] : $otherPrompt . $userPrompt['positive'],
            'negative_prompt' => $userPrompt['negative'],
            'sampler_name' => 'Euler',
            'steps' => 50,
            'batch_size' => 2,
            'seed' => -1,
            'n_iter' => 1,
            'tiling' => false, //this is the default
            'width' => 512,
            'height' => 512,
        ];

        $res_prompt = null;
        $usageTrackingData = [
            'requestData' => $req,
            'prompts' => [
                'user' => $req['prompt'],
                'other' => $otherPrompt,
            ],
            'app' => $slug,
            'response_usage' => '',
        ];
        try {
            $filter_nsfw = json_decode($this->filter_nsfw($params['json']['prompt']), true);

            if($filter_nsfw['success']){
                $res_prompt = $client->post('https://imagine.ai-pro.org/e/sdapi/v1/txt2img', $params);
                $response = json_decode($res_prompt->getBody(),true);
                $response['success'] = 1;
                $response['message'] = 'OK';
            }else{
                $response = $filter_nsfw;
            }

            $res = [
                'success' => $response['success'],
                'message' => $response['message'], // TODO: change to OK
                'data' => $response,
                'usageCounter' => json_encode([
                    'total_tokens' => $params['json']['batch_size'],
                    'size' => [
                        'width' => $params['json']['width'],
                        'height' => $params['json']['height'],
                    ],
                ]),
            ];
            $usageTrackingData['ai_response'] = json_encode($res['data']);
            $usageTrackingData['response_usage'] = $res['usageCounter'];
        } catch (\Exception$e) {
            $res = [
                'success' => 0,
                'message' => 'Sorry, too many requests. Please try again in a bit! [err#158]',
                'data' => $e,
            ];
            $usageTrackingData['ai_response'] = $res['data'];
            $usageTrackingData['promptResponseData'] = $res;
            $this->trackUsage('failed', $usageTrackingData);
            return $res;
        }

        $usageTrackingData['promptResponseData'] = $response;

        $usageTrackingData['promptResponseData'] = '';
        $usageTrackingData['ai_response'] = '';
        $usageTrackingData['promptResponseData'] = '';

        // btutilDebug($usageTrackingData);

        $this->trackUsage('success', $usageTrackingData);
        // unset($res['usageCounter']);
        return $res;
    }

    private function imageVariation(){
    }

		private function getWPRegisterUrl() {
			$wp_flow = btflag_cookie('flow', '');
			$register_url = '';
			switch ($wp_flow) {
				case '02':
						$register_url = '/register-b';
				break;
				case '03':
						$register_url = '/register-x';
				break;
				case '04':
						$register_url = '/select-account-type-d'; //splash
				break;
				case '05':
						$register_url = '/aurora-register';
				break;
				default:
						$register_url = $register_url;
			}
			return $register_url;
		}

    private function filter_nsfw($prompt){
        $response['success'] = 1;
        $nsfw_words = file_get_contents(VENDORPATH . "/btapp/btcore/nsfw.txt");
        $nsfw_words = explode("\n", $nsfw_words);
        $matched_words = [];
        $input_data = strtolower($prompt);

        // Filter the NSFW words
        foreach ($nsfw_words as $nsfw_word) {
            $nsfw_word = trim($nsfw_word);
            if ((strpos($input_data, $nsfw_word) !== false) OR // Check for exact match
            (strpos($input_data, ' '.$nsfw_word) !== false || strpos($input_data, $nsfw_word.' ') !== false) OR // Check for partial match with a space before or after the NSFW word
            (preg_match('/[^\w]'.$nsfw_word.'[^\w]/i', $input_data))) { // Check for partial match with punctuation or special characters before or after the NSFW word
                $matched_words[] = '"<strong>' . $nsfw_word . '</strong>"';
            }
        }

        if (!empty($matched_words)) {
            $response['success'] = 0;
            $error_message = 'The combination of keywords you entered does not meet our standards for safe viewing. Please try a different set of keywords.';
            $error_message .= '<br>Invalid keyword(s): ' . implode(', ', $matched_words);
            $response['message'] = $error_message;
        }

        return json_encode($response);

    }
}
