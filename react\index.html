<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vite + React + TS</title>
  </head>
  <script>
    let ver = new Date().getTime();
    let btutilAsset = document.createElement("script");
    btutilAsset.setAttribute(
      "src",
      "https://staging.api.ai-pro.org/ext-app/js/btutil-all-v2c.min.js?ver=" +
        ver,
    );
    document.head.appendChild(btutilAsset);
  </script>

  <body>
    <div id="root"></div>
    <div id="modal-container" style="display: none">
      <div id="modal-content">
        <div id="modal-subtitle"></div>
        <h2 id="modal-title"></h2>
        <p id="modal-desc"></p>
        <a id="modal-cta" href="#" target="_parent"></a>
      </div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
