body {
  font-family: 'Poppins', sans-serif;
}
.btn-success{
  font-weight: bold;
}
textarea::placeholder {
  color: #BCBCBC;
  font-weight: 300 !important;
}
textarea {
  font-weight: 400 !important;
  font-size: 0.75rem !important;
  color: #343434 !important;
}
label {
  font-weight: 700;
  font-size: 15px;
}
.settings-container p{
  font-size: 12px;
  font-weight: bold;
  text-align: left;
}
.suggestion {
  margin-top: 25px
}
.suggestion-items-container{
  text-align: initial;
}
.detailed_description {
  font-size: 10px;
  font-style: italic;
  font-weight: 100;
}
.item{
  display: inline-block;
  padding: 2px 15px;
  font-size: 12px;
  border: 1px solid #6D6D6D;
  border-radius: 15px;
  color: #6D6D6D;
  margin: 0 5px 7px 0;
  cursor: pointer;
  -webkit-user-select: none; /* Safari */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE10+/Edge */
  user-select: none; /* Standard */
}
.item.active{
  color: #fff;
  background: #6D6D6D;
}
.btn-outline-light {
  width: 90%;
  padding: 11px;
  display: block;
}
.btn-container{
  justify-content: center;
  align-items: center;
  display: flex;
  position: absolute;
  height: 100%;
  width: 100%;
}
.img-container img{
    max-width: 100%;
    max-height: 100%;
    object-fit: contain; /* Adjust this property as needed (e.g., 'cover', 'fill', 'contain') */
}
.loader {
  position: relative;
  color: #28a745;
  width: 110px;
  height: 110px;
  margin: 0 auto;
  display: block;
  text-align: center;
  position: inherit;
  margin-top: 15%;
}
.border {
  border: 1px solid #ced4da !important;
  -webkit-transition: box-shadow 1s ease-out;
  -moz-transition: box-shadow 1s ease-out;
  -o-transition: box-shadow 1s ease-out;
  transition: box-shadow 1s ease-out;
}
.rounded {
  border-radius: 1rem !important;
}
.border:hover {
  box-shadow: 0px 0px 15px 4px rgba(0,0,0,0.1);
}
.material-icons {
  font-size: 12px;
  vertical-align: super;
}
.btn {
  font-size: .8rem;
}
#generate {
  font-weight: bold;
  background-color: #28A745;
  border-color: #28A745;
}
.badge {
  font-size: 13px;
  display: none;
}
.high-res, .low-res {
  font-size: .7rem;
}
.high-res:hover {
  color: #fff !important;
}
.loading-icon {
  width: 6rem;
  height: 6rem;
}
.dl-loading-icon {
  width: 1rem;
  height: 1rem;
  margin-right: 10px;
}
.popup {
  position: fixed;
  top: 20%;
  z-index: 999999999;
  width: 450px;
  height: 330px;
  background-color: #fff;
  display: none;
  justify-content: center;
  align-items: center;
  box-shadow: 0 0px 15px -5px black;
  text-align: center;
  padding: 50px 20px 30px;
  border-radius: 10px;
  left: 50%;
  transform: translateX(-50%);
}
.popup .btn {
  display: block;
  padding: 10px !important;
  width: 200px;
  font-weight: bold;
  text-align: center;
  margin: 30px auto;
}
.popup-overlay {
  position: fixed;
  background-color: #000000;
  opacity: 0.9;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  margin-top: 0 !important;
  display: none;
}
.popup-content {
  display: block;
  font-size: 2rem;
  font-weight: 600;
}
.popup_desc {
  font-weight: 300;
}
.model-dropdown {
  height: 19.8rem;
}
.model-dropdown p {
  margin-bottom: 0;
}
.model-dropdown .detailed_description {
  display: block;
  margin-bottom: 1rem;
} 
.model-dropdown button{
  color: #6D6D6D;
  border-radius: .30rem;
  border-color: #6c757d;
  background-color: #fff;
}
.dropdown-item{
  font-weight: 300;
  cursor: pointer;
}
.dropdown-menu {
  padding: 0;
  font-size: 0.8rem; 
}
.dropright .dropdown-menu {
  left: 15px !important;
}
.dropdown-item.active, .dropdown-item:active {
  background-color: #6D6D6D;
}
.dropdown-item.active:nth-child(1){
  border-radius: 1rem 1rem 0 0;
}
.dropdown-item.active:last-child{
  border-radius: 0 0 1rem 1rem;
}
.lb-data .lb-close {
  position: absolute;
  top: 7px;
  margin-left: 0px;
  z-index: 99;
}

.text-pretty {
  text-wrap: pretty;
}

@media (max-width : 530px) {
  .popup {
    height: auto;
  }
}
@media (max-width : 414px) {
  .popup {
    width: 91vw;
  }
}
@media (max-width : 375px) {
  .popup {
    width: 89vw;
  }
}
@media (max-width : 320px) {
  .popup {
    width: 88vw;
  }
}
@media (max-width: 768px) { 
  .aspect-container {
    height: unset;
  }
  .container-flex { display: flex; flex-flow: column; }
  .mobile-first { order: 1; }
  .mobile-second { order: 2;  }
}

#lightboxOverlay {
  max-height: 100vh;
  max-width:100%;
}

#lightbox {
  top:0 !important;
}

#lightbox .lb-outerContainer {
  height: calc(100vh - 5px)!important;
  background: transparent;
  display: flex;
  justify-content: center;
}

#lightbox .lb-outerContainer img {
  width: auto !important;
  height:100% !important;
}