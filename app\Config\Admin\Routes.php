<?php

/*
 * --------------------------------------------------------------------
 * Admin Route Definitions
 * --------------------------------------------------------------------
 */

$routes->group("", ["namespace" => "App\Controllers\Admin"], function($routes){
    $routes->get("/", "FormsLibrary::index");
});

$routes->group("admin", ["namespace" => "App\Controllers\Admin"], function($routes){
    $routes->get("/", "FormsLibrary::index");
    $routes->get("home", "FormsLibrary::index");
    $routes->get("forms-library", "FormsLibrary::index");
    // $routes->match(["get"], "forms-library", "FormsLibrary::index");
});

$routes->group("ai", ["namespace" => "App\Controllers\ArtificialIntelligence"], function($routes){
    $routes->get("builder", "Builder::index");
});

$routes->group("btwizard", ["namespace" => "App\Controllers\Btwizard"], function($routes){
    $routes->get("builder", "Builder::index");
    $routes->get("crud/list", "Crud::list");
});

$routes->group("home", ["namespace" => "App\Controllers\Admin"], function($routes){
    $routes->get("/", "FormsLibrary::index");
}); 

