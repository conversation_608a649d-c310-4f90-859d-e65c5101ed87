{% extends 'base_A_template.twig' %}

{% block head_styles %}
{{ parent() }}
<link rel='stylesheet' href='https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css'>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,200;0,300;0,500;1,400&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/icon?family=Material+Icons"rel="stylesheet">
<link rel="stylesheet" href="{{BASE.assets_common}}css/dreamphoto.css">
<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.4/css/lightbox.min.css" integrity="sha512-ZKX+BvQihRJPA8CROKBhDNvoc2aDMOdAlcm7TUQY+35XYtrd3yh95QOOhsPDQY9QnKE0Wqag9y38OIgEvb88cA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css" integrity="sha512-5A8nwdMOWrSz20fDsjczgUidUBR8liPYU+WymTZP1lmY9G6Oc7HlZv156XqnsgNUzTyMefFTcsFH/tnJE/+xBg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<style>
  @font-face {
    font-family: 'Futura Hv BT Heavy';
    src: url('./assets/common/fonts/Futura Hv BT Heavy.ttf') format('truetype');
  }
  .img-content {
    position: relative;
    overflow: hidden;
  }
  .img-container.blur-img img {
    filter: blur(20px);
  }
  .img-container:not(.blur-img) .view-img { display: none; }
  .view-img {
    position: absolute;
    top: calc(50% - 14px);
    left: calc(50% - 58px);
    cursor: pointer;
    font-size: 12px;
    background: #ffffff61;
    padding: 5px 15px;
    border-radius: 5px;
  }
</style>

{% endblock %}


{% block head_scripts %}
{{ parent() }}
<script>
  let ver = new Date().getTime();
  let btutilAsset = document.createElement('script');
  btutilAsset.setAttribute('src', '{{ PAGE.btutil_auth_url }}' + ver);
  document.head.appendChild(btutilAsset);
</script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.4/js/lightbox-plus-jquery.min.js" integrity="sha512-U9dKDqsXAE11UA9kZ0XKFyZ2gQCj+3AwZdBMni7yXSvWqLFEj8C1s7wRmWl9iyij8d5zb4wm56j4z/JVEwS77g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script>
    lightbox.option({
      'fitImagesInViewport': true
    })
</script>

{% endblock %}

{% block body_content %}
{{ parent() }}
<div class="container p-3">
		<div class="d-flex my-5 justify-content-center align-items-center">
			<img src="./assets/dream-photo-icon.png" height="48px" alt="Dream Photo logo">
			<h1 style="font-family: Futura Hv BT Heavy;font-size: 30px;margin-bottom: 0;">DreamPhoto</h1>
		</div>
    <div class="row container-flex">
      <aside class="col-12 col-md-4 mobile-second">
        <div class="settings-container rounded border border-dark p-5 mb-5">
          <div class="suggestion-container mt-3 mb-3">
            <label role="contentinfo" aria-label="Suggested Prompts">Suggested Prompts</label>
            <div class="suggestion-items-container">
              <div
                class="sugg-item item"
                tabindex="0"
                role="button"
                aria-label="Portrait"
              >
                Portrait
              </div>
              <div
                class="sugg-item item"
                tabindex="0"
                role="button"
                aria-label="Scenic View"
              >
                Scenic View
              </div>
              <div
                class="sugg-item item"
                tabindex="0"
                role="button"
                aria-label="Food"
              >
                Food
              </div>
              <div
                class="sugg-item item"
                tabindex="0"
                role="button"
                aria-label="Animal"
              >
                Animal
              </div>
              <div
                class="sugg-item item"
                tabindex="0"
                role="button"
                aria-label="Vehicle"
              >
                Vehicle
              </div>
              <div
                class="sugg-item item"
                tabindex="0"
                role="button"
                aria-label="Architecture"
              >
                Architecture
              </div>
              <div
                class="sugg-item item"
                tabindex="0"
                role="button"
                aria-label="Monster"
              >
                Monster
              </div>
              <div
                class="sugg-item item"
                tabindex="0"
                role="button"
                aria-label="Anime"
              >
                Anime
              </div>
            </div>
          </div>
          <div class="suggestion-container aspect-container mt-3 mb-3">
            <label role="contentinfo" aria-label="Aspect Ratio">
              Aspect Ratio
              <span class="badge text-white" style="background-color:#0071EB;" role="contentinfo" arial-label="Pro">
                Pro
              </span>
            </label>
            <div class="suggestion-items-container">
              <div
                class="aspect-item item active"
                data-width="512"
                data-height="512"
                role="button"
                aria-label="Square (1:1)"
                tabindex="0"
              >
                Square (1:1)
              </div>
              <div
                class="aspect-item item"
                data-width="640"
                data-height="896"
                role="button"
                aria-label="Portrait (3:4)"
                tabindex="0"
              >
                Portrait (3:4)
              </div>
              <div
                class="aspect-item item"
                data-width="896"
                data-height="640"
                role="button"
                aria-label="Landscape (4:3)"
                tabindex="0"
              >
                Landscape (4:3)
              </div>
              <div
                class="aspect-item item"
                data-width="576"
                data-height="1024"
                role="button"
                aria-label="Mobile (9:16)"
                tabindex="0"
              >
                Mobile (9:16)
              </div>
              <div
                class="aspect-item item"
                data-width="1024"
                data-height="576"
                role="button"
                aria-label="Desktop (16:9)"
                tabindex="0"
              >
                Desktop (16:9)
              </div>
            </div>
          </div>
          <div class="dropdown model-dropdown">
            <label role="contentinfo" aria-label="Model" >Model <span class="badge text-white" style="background-color:#0071EB;" role="contentinfo" arial-label="Pro">Pro</span></label>
            <span class="detailed_description" role="contentinfo" aria-label="Determines the overall style & base resolution" >Determines the overall style & base resolution</span>
            <button class="btn btn-secondary dropdown-toggle btn-block text-pretty" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
              Realistic (default)
            </button>
            {% if PAGE.btform.dp_models %}
            <div class="dropdown-menu rounded btn-block" aria-labelledby="dropdownMenuButton">
              <a class="dropdown-item active" tabindex="0">Realistic (default)</a>
              {# iterate through available models in ENV #}
              {% for model_name, checkpoint in PAGE.btform.dp_models %}
                <a class="dropdown-item" data-model="{{ checkpoint }}" tabindex="0">{{ model_name }}</a>
              {% endfor %}
            </div>
            {% endif %}
          </div>
        </div>
      </aside>

      <div class="col-12 col-md-8 mobile-first">
        <div class="row">
          <div class="prompting-container rounded border border-dark p-5 col-12 mb-5 text-center text-md-left">
            <div class="row">
              <div class="col-md-6 mb-4 mb-md-0">
                <label
                  class="text-sm-center"
                  for="positivePrompt"
                  title="Detailed descriptions of the image you want to create"
                  role="contentinfo"
                >
                  Positive prompt
                  <span
                    class="material-icons"
                    data-toggle="tooltip"
                    data-placement="top"
                    title="Detailed descriptions of the image you want to create"
                    role="tooltip"
                    tabindex="0"
                  >
                    help_outline
                  </span>
                </label>
                <textarea
                  name="positivePrompt"
                  aria-required="true"
                  aria-label="Positive prompt"
                  class="form-control rounded"
                  id="positivePrompt"
                  cols="20"
                  rows="4"
                  placeholder="A really cute cat, running through the snow, wearing pink shoes"
                ></textarea>
              </div>
              <div class="col-md-6 mb-4 mb-md-0">
                <label
                  class="text-sm-center"
                  for="negativePrompt"
                  title="Detailed descriptions of the things you do not want to see in your image"
                  role="contentinfo"
                >
                  Negative prompt
                  <span
                    class="material-icons"
                    data-toggle="tooltip"
                    data-placement="top"
                    title="Detailed descriptions of the things you do not want to see in your image"
                    role="tooltip"
                    tabindex="0"
                  >
                    help_outline
                  </span>
                </label>
                <textarea
                  name="negativePrompt"
                  aria-required="false"
                  aria-label="Negative prompt"
                  class="form-control rounded"
                  id="negativePrompt"
                  cols="20"
                  rows="4"
                  placeholder="Extra limbs, extra fingers, weird eyes, fangs, and alien-looking body"
                ></textarea>
              </div>
              <div class="cta_con mx-auto mt-0 mt-md-4">
                <button class="btn text-white rounded" id="generate" type="button">Generate Image</button>
              </div>
            </div>
          </div>

          {# generated images container #}
          <div class="container">
            <div class="row" id="outputResult" >
              {# images #}
            </div>
          </div>

        </div>
      </div>
      </div>
    </div>
</div>

<div class="popup-overlay modal"></div>
<div class="popup container mx-auto modal" tabindex="-1">
  <h1 class="popup-content mb-4">Get Full Access to DreamPhoto</h1>
  <p class="text-md popup_desc">Upgrade to the most powerful AI image generator ever, created just for you. Get full access to all advanced features by clicking Continue below.</p>
    <button type='button' class="btn btn-primary" onClick="redirectToUpgrade()" tabindex="1"> Continue</button>
</div>
<div id="modal-container" style="display: none">
  <div id="modal-content">
    <div id="modal-subtitle"></div>
    <h2 id="modal-title"></h2>
    <p id="modal-desc"></p>
    <a id="modal-cta" href="#" target="_parent"></a>
  </div>
</div>

{% endblock %}


{% block body_footer %}
{{ parent() }}
{% endblock %}



{% block body_hidden_content %}
{{ parent() }}
{% endblock %}


{% block body_scripts %}
{{ parent() }}
<script src="https://cdn.jsdelivr.net/npm/popper.js@1.12.9/dist/umd/popper.min.js" integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.0.0/dist/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.4/css/lightbox.min.css" integrity="sha512-ZKX+BvQihRJPA8CROKBhDNvoc2aDMOdAlcm7TUQY+35XYtrd3yh95QOOhsPDQY9QnKE0Wqag9y38OIgEvb88cA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<script type='text/javascript'>
  document.addEventListener('readystatechange', e => {
  if(document.readyState === "complete"){
      badgeChecker();
    }
  });

  const showAgeConfirmation = (fn = ()=>{}) => {
    Swal.fire({
      title: "Confirmation Required",
      html: "Please confirm that you are 18 years old or above.",
      showCancelButton: true,
      confirmButtonText: "Confirm",
      confirmButtonColor: "#16a34a"
    }).then((result) => {
      if (result.isConfirmed) {
        fn();
      }
    });
  }

  var isLegalAge = false;
  const generateBtn = document.getElementById('generate');
  const positivePrompt = document.getElementById('positivePrompt');
  const negativePrompt = document.getElementById('negativePrompt');
  const suggestedPrompt = document.querySelectorAll('.sugg-item');
  const aspectRatioItems = document.querySelectorAll('.aspect-item');
  const highResolutionButtons = document.querySelectorAll('.high-res');
  const dropDownItems = document.querySelectorAll('.dropdown-item');
  let outputWidth = isProChecker() ? '1024' : '512';
  let outputHeight = isProChecker() ? '1024' : '512';
  let checkpoint = 'RealitiesEdgeXL_4';

  //Suggested Prompts Click Handler
  suggestedPrompt.forEach((suggItem) => {
    const suggestedPromptEventHandler = (event) => {
      const target = event.target;
      const itemText = target.innerText;
      const suggestionData = {
        'items': [
          {
            'text': 'Portrait',
            'positivePrompt': [
              'Close up portrait of a white-haired old man, Exquisite detail, 30-megapixel, 4k, 85-mm-lens, sharp-focus, f:8, ISO 100, shutter-speed 1:125, diffuse-back-lighting, award-winning photograph, small-catchlight, High-sharpness, facial-symmetry',
              'Closeup portrait of a cyborg, mechanical parts, ultra realistic, concept art, intricate details, eerie, highly detailed, photorealistic, 8k, unreal engine. art by artgerm and greg rutkowski and charlie bowater and magali villeneuve and alphonse mucha, golden hour, cyberpunk, robotic, steampunk, neon colors, metallic textures.',
              'Professional portrait photograph of a gorgeous Norwegian girl in winter clothing with long wavy blonde hair, ((sultry flirty look)), freckles, beautiful symmetrical face, cute natural makeup, ((standing outside in snowy city street)), stunning modern urban upscale environment, ultra realistic, concept art, elegant, highly detailed, intricate, sharp focus, depth of field, f/1. 8, 85mm, medium shot, mid shot, (centered image composition), (professionally color graded), ((bright soft diffused light)), volumetric fog, trending on instagram, trending on tumblr, hdr 4k, 8k'
            ],
            'negativePrompt':[
              '(bonnet), (hat), (beanie), cap, (((wide shot))), (cropped head), bad framing, out of frame, deformed, cripple, old, fat, ugly, poor, missing arm, additional arms, additional legs, additional head, additional face, multiple people, group of people, dyed hair, black and white, grayscale'
            ]
          },
          {
            'text': 'Scenic View',
            'positivePrompt': [
              'View of the northern lights at night time, seen in Alaska, Canon RF 16mm f:2.8 STM Lens, hyperrealistic photography, style of Unsplash and National Geographic',
              'Sea of Endless waves with the sparkling reflection of the sun, golden hour, Canon RF 16mm f:2.8 STM Lens,  hyperrealistic photography, style of unsplash and National Geographic',
              'Hills of the Scotland highlands, misty fog, Canon RF 16mm f:2.8 STM Lens, award winning photography, by National Geographic and Unsplash'
            ],
          },
          {
            'text': 'Food',
            'positivePrompt': [
              'sliced ​​rib-eye steak on a red plate, with separate stir-fry vegetables and extra fried potatoes in a bowl, on a steel table + 4k resolution + photorealistic, ambient light  --beta --upbeta',
              'very healthy food plate with avocados, tomatoes, eggs and other healthy food, 8 k resolution, food photography, studio lighting, sharp focus, hyper - detailed',
              'royal fast food, burger, fries, coffee, nuggets, salad, steam coming out, hyper detailed, intricate photorealistic, 8k, UHD, delicious, amazing, epic, epic contour light, volumetric light --v 4 --upbeta'
            ],
          },
          {
            'text': 'Animal',
            'positivePrompt': [
              'majestic Lion, mane made of fire, illuminating the surrounding area creating beautiful stunning shadows around it and over the Lion’s head.  painted portrait ,fantasy, intricate, elegant, highly detailed, digital painting, artstation, concept art, smooth, sharp focus, illustration, art by gaston bussiere and alphonse mucha',
              'photography print of taxidermy Whitehead Eagle, spread wings, majestic, stuffing of twigs and moss falling out, next on a cliff background, dappled lighting, backlit, by ellen jewett and Dariusz Zawadzki and Brian froud and tom bagshaw, real, realistic, 8k, high resolution, high definition, wildlife photography --ar 5:3 --s 3500',
              'a shark themed mecha walking on the water, diffuse lighting, fantasy, highly detailed, photorealistic, digital painting, artstation, illustration, concept art, smooth, sharp focus'
            ],
          },
          {
            'text': 'Vehicle',
            'positivePrompt': [
              'concept art of motorcycle, highly detailed painting by dustin nguyen, akihiko yoshida, greg tocchini, greg rutkowski, cliff chiang, 4 k resolution, trending on artstation, 8k',
              'a concept spaceship made out of Tesla parts, Ferrari parts, F1 racing parts :: x-wing, concept art, art station trends, airbase setting, space station setting, unreal engine render, octane render, 4k, volumetric lighting, photorealistic, new type, sleek design, ultra aerodynamic, spacecraft, dynamic style, F-22 fighter jet, concept car, stealth bomber, wide angle --ar 5:3',
              'sport car, shimmery metallic blue job paint, aggressive look, tuned, bmw and corvette mix, full car only, in motion, (bright headlights on:1.6), (at night:1.6), high speed, (motion blur:1.3), (driver:1.6), movie action scene, (Need for Speed:1.3), wet road, illegal racing game, (industrial cityscape in background:1.5), (detailed stunning environment:1.5), (foggy), moody dark atmosphere, bright headlights, neon underground aesthetics, (sci-fi), cyberpunk, blade runner, cinematic, cover art, (low front angle), full view of a sports car, intricate, highly detailed, digital painting, digital art, artstation, concept art, (complementary colors:1.5), (color contrast:1.5), best quality masterpiece, photorealistic, detailed, sharp focus, 8k, HDR, shallow depth of field, broad light, high contrast, backlighting, bloom, light sparkles, chromatic aberration, sharp focus, RAW color photo'
            ],
          },
          {
            'text': 'Architecture',
            'positivePrompt': [
              'house, golden hour, Canon RF 16mm f:2.8 STM Lens,  hyperrealistic photography, style of unsplash and National Geographic',
              'a lush apartment building covered in vines by studio ghibli sticker:: a die cut studio ghibli sticker of an apartment building in a lush jungle city --ar 9:16',
              'A man smoking in the balcony of his condominium surrounded by windows with lush vegetation, wooden floor, high ceiling, beige blue salmon pastel palette, interior design magazine, cozy atmosphere'
            ],
          },
          {
            'text': 'Monster',
            'positivePrompt': [
              'Closeup portrait of a monster with glowing eyes and sharp teeth, dark shadows, foggy background, highly detailed, photorealism, concept art, digital painting, art by yahoo kim, max grecke, james white, viktor hulík, fabrizio bortolussi.',
              'a humanoid monster made of metal and flensed muscle , flensed muscle, gears, monster concept art, monster art, --c 100 --ar 3:4',
              'A seamless pattern of surreal colorful fluffy elaborate and detailed futuristic monsters, complicated surreal monsters, fun patterns, detailed accents, monster details, colorful --v4'
            ],
          },
          {
            'text': 'Anime',
            'positivePrompt': [
              'game 2d art, image of a young man with light gray hair, 16 years old, green eyes, white tank top, steampunk clothes, anime, dynamic pose, ultra-detailed, white background, HD, design art by Hideo Minaba, Yuya Nagai, Ryoji Ohara, Ryota Murayama and Hitomi Yoshimura, granblue fantasy --v4',
              'game 2d art, image of a young girl with light blonde hair, 14 years old, green eyes, white skirt, anime, dynamic pose, ultra-detailed, white background, HD, design art by Hideo Minaba, Yuya Nagai, Ryoji Ohara, Ryota Murayama and Hitomi Yoshimura, granblue fantasy, artgerm --v4'
            ],
          },
        ]
      }
      const suggestion = suggestionData.items.find(item => item.text === itemText);

      if (suggestion) {
        const positivePrompts = suggestion.positivePrompt;
        const negativePrompts = suggestion.negativePrompt || [];
        const randomPositivePrompt = positivePrompts[Math.floor(Math.random() * positivePrompts.length)];
        const randomNegativePrompt = negativePrompts.length > 0 ? negativePrompts[Math.floor(Math.random() * negativePrompts.length)] : '';
        const activeItems = document.querySelectorAll('.sugg-item.item.active');
        activeItems.forEach(function(item) {
          item.classList.remove('active');
        });

        target.setAttribute('data-positive-prompt', randomPositivePrompt);
        target.setAttribute('data-negative-prompt', randomNegativePrompt);

        const positivePrompt = target.getAttribute('data-positive-prompt');
        const negativePrompt = target.getAttribute('data-negative-prompt');
      }

      if (target.classList.contains('active')){
        positivePrompt.value = '';
        negativePrompt.value = '';
      } else {
        suggItem.classList.add('active');
        positivePrompt.value = target.getAttribute('data-positive-prompt');
        negativePrompt.value = target.getAttribute('data-negative-prompt');
      }

    }
    suggItem.addEventListener('click',  suggestedPromptEventHandler );
    suggItem.addEventListener("keypress",  (event) => {
      if (event.key === "Enter") {
        event.preventDefault();
        suggestedPromptEventHandler(event)
      }
    });


  });

  //Aspect Ratio Click Handler
  aspectRatioItems.forEach((aspectItem) => {
    const aspectRatioItemsEventHandler = (event) => {
      const desiredWidth = aspectItem.getAttribute('data-width');
      const desiredheight = aspectItem.getAttribute('data-height');
      const isActive = aspectItem.classList.contains('active');
      aspectRatioItems.forEach(btn => {
        if(isProChecker()){
          btn.classList.remove('active');
        }
      });
      if (!isActive) {
        if(desiredWidth !== 512 && !isProChecker()){
          if (typeof mixpanel !== "undefined") {
            mixpanel.track('aspect-ratio', {
                'option': aspectItem.textContent.trim()
            });
          }
          showModal();
        }else{
          aspectItem.classList.add('active');
          outputWidth = desiredWidth;
          outputHeight = desiredheight;
        }
      }
    }
    aspectItem.addEventListener('click', aspectRatioItemsEventHandler);

      aspectItem.addEventListener("keypress",  (event) => {
      if (event.key === "Enter") {
        event.preventDefault();
        aspectRatioItemsEventHandler(event)
      }
    });
  });

  //Model handler
  dropDownItems.forEach((dropDownItem) => {
    dropDownItem.addEventListener('click', (event) => {
      const isActive = dropDownItem.classList.contains('active');
      dropDownItems.forEach(btn => {
        if(isProChecker()){
          btn.classList.remove('active');
        }
      });
      if (!isActive) {
        if(dropDownItem.getAttribute("data-model") !== checkpoint && !isProChecker()){
          showModal();
          const dropdown = document.querySelector('.model-dropdown');
          dropdown.addEventListener('click', function(event) {
              if (event.target.classList.contains('dropdown-item')) {
                  dropdown.querySelector('.dropdown-item.active').classList.remove('active');
                  event.target.classList.add('active');
                  const selectedModel = event.target.textContent.trim();

                  if (typeof mixpanel !== "undefined") {
                    mixpanel.track('model', {
                        'option': selectedModel
                    });
                  }
              }
          });
        }else{
          dropDownItem.classList.add('active');
          checkpoint = dropDownItem.getAttribute('data-model');
          $('#dropdownMenuButton').text(dropDownItem.text);
        }
      }
    });
  });

  //Generate Click handler
  generateBtn.addEventListener('click', async () => {
    if (generateBtn.disabled) {
      return;
    }
    generateBtn.disabled = true;
    generate();
  });

  //Download High Res handler

    function downloadHighResHandler(event) {
    const target = event.target;
    if (target.classList.contains('high-res')) {
      if (!isProChecker()) {
        if (typeof mixpanel !== "undefined") {
          mixpanel.track('high-res', {
              'option': true
          });
        }
        showModal();
      }
    }

    if (target.classList.contains('popup-overlay')) {
      const modal = document.querySelectorAll('.modal');
      for (var i=0;i<modal.length;i+=1){
        modal[i].style.display = '';
      }
    }
  }
  // onclick event
  document.addEventListener('click', downloadHighResHandler);
  // on enter
  document.addEventListener("keypress",  (event) => {
    if (event.key === "Enter") {
      event.preventDefault();
      downloadHighResHandler(event)
    }
  });


  $(function () {
    $('[data-toggle="tooltip"]').tooltip()
  })

  positivePrompt.addEventListener('input', function(evt) {
    if (evt.target.value === '') {
      const activeSuggItems = document.querySelectorAll('.sugg-item.item.active');
      activeSuggItems.forEach(function(item){
        item.classList.remove('active');
      });
    }
  });

function handleDownloadHighResolution(imageList, imageData) {
  const upscale_url = '{{ BASE.base_url }}api/aipsd/v2/dream-photo-upscale';
  const payload = {
    resize_mode: 0,
    show_extras_results: true,
    gfpgan_visibility: 0,
    codeformer_visibility: 0,
    codeformer_weight: 0,
    upscaling_resize: 1.5,
    upscaling_resize_w: imageData.width,
    upscaling_resize_h: imageData.height,
    upscaling_crop: true,
    upscaler_1: "4x-UltraSharp",
    upscaler_2: "None",
    extras_upscaler_2_visibility: 0,
    imageList: imageList,
    upscale_first: false,
  };
  const formData = new FormData();
  formData.append('payload', JSON.stringify(payload));
  const requestOptions = { method: 'POST', body: formData };
  fetch(upscale_url, requestOptions)
  .then(response => {
    return response.json();
  })
  .then((output) => {
    const link = document.querySelectorAll('.high-res');
    const loader = document.querySelectorAll('.dl-loading-icon');
    for(let i = 0; i < output.data.images.length; i++){
      link[i].href = `data:image/png;base64,${output.data.images[i]}`;
      link[i].download = `dream-photo-hd-${i}.png`;
      link[i].classList.remove("disabled");
      link[i].removeAttribute("aria-disabled");
      const loadingIcon = loader[i];
      if (loadingIcon) {
        loadingIcon.parentNode.removeChild(loadingIcon);
      }

    }
  })
  .catch(error => {
    console.log(error)
  });
};

function generate() {
  const outputContainer = document.getElementById('outputResult');
  const txt2img_url = '{{ BASE.base_url }}api/aipsd/v2/dream-photo-create';
  const slug = '{{ PAGE.slug }}';
  const surfToken = '{{ PAGE.surfToken.hash }}';
  const positivePrompt = document.querySelector('#positivePrompt');
  const negativePrompt = document.querySelector('#negativePrompt');
  if(positivePrompt.value === ''){
    $('#positivePrompt').focus();
    return;
  }
  const formData = new FormData();
  const payload = {
    positive : positivePrompt.value,
    negative : negativePrompt.value,
    height : outputHeight,
    width : outputWidth,
    model: checkpoint
  };
  const requestOptions = { method: 'POST', body: formData };
  formData.append('payload', JSON.stringify(payload));
  formData.append('slug', slug);
  formData.append('surfToken', surfToken);
  outputContainer.style.justifyContent = 'center';
  outputContainer.innerHTML = `<div class="loading-icon spinner-border mb-5 mb-md-0" role="status"><span class="sr-only">Loading...</span></div>`;
  outputContainer.scrollIntoView({ behavior: 'smooth', block: 'end'});
  //Api Call
  fetch(txt2img_url, requestOptions)
  .then(response => {return response.json();})
  .then((output) => {
    if (output.success == 2) {
        const modalsubTitle = document.querySelector('#modal-subtitle');
        const modalTitle = document.querySelector('#modal-title');
        const modalDesc = document.querySelector('#modal-desc');
        const modalCta = document.querySelector('#modal-cta');

        modalsubTitle.innerHTML = output.modal.subTitle;
        modalTitle.innerHTML = output.modal.title;
        modalDesc.innerHTML = output.modal.desc;
        modalCta.innerHTML = output.modal.ctaLabel;
        modalCta.href = output.modal.redirectUrl;

        const modalContainer = document.querySelector('#modal-container');
        modalContainer.style.display = 'block';
        return;
    }
    if (output.success) {
      var htmlOutput = '';
      for(let i = 0; i < output.data.images.length; i++){
        outputContainer.style.removeProperty('justify-content');
        if (output.data.blur && output.data.blur[i]) {
          htmlOutput += `
          <div class="col-12 col-md-5 mx-auto mb-5 w-75 p-5 rounded border">
            <div class="img-container blur-img nsfw">
              <div class="img-content">
                <a>
                  <img class="position-relative" src="data:image/png;base64,${output.data.images[i]}" alt="">
                </a>
                <a class="view-img"><i class="fa fa-eye" aria-hidden="true"></i> View Image</a>
              </div>
              <a href="data:image/png;base64,${output.data.images[i]}" download="dream-photo-low-${[i]}" class="btn btn-outline-dark btn-block mt-5 low-res" role="button" aria-pressed="true">Download Low Resolution</a>
              <a tabindex="0" class="btn btn-outline-dark btn-block high-res ${isProChecker() ? 'disabled' : ''}" role="button" data-index="${[i]}">
                <div class="dl-loading-icon spinner-border ${!isProChecker() ? 'd-none' : ''}" role="status"><span class="sr-only">Loading...</span></div>
                Download High Resolution
                ${!badgeChecker() ? '<span class="badge badge-primary" style="background-color:#0071EB;" >Pro</span>' : ''}
              </a>
            </div>
          </div>
          `;
        } else {
          htmlOutput += `
          <div class="col-12 col-md-5 mx-auto mb-5 w-75 p-5 rounded border">
            <div class="img-container">
                <a href="data:image/png;base64,${output.data.images[i]}" data-lightbox="dream-photo-${[i]}">
                  <img class="position-relative" src="data:image/png;base64,${output.data.images[i]}" alt="">
                </a>
                <a href="data:image/png;base64,${output.data.images[i]}" download="dream-photo-low-${[i]}" class="btn btn-outline-dark btn-block mt-5 low-res" role="button" aria-pressed="true">Download Low Resolution</a>
                <a tabindex="0" class="btn btn-outline-dark btn-block high-res ${isProChecker() ? 'disabled' : ''}" role="button" data-index="${[i]}">
                  <div class="dl-loading-icon spinner-border ${!isProChecker() ? 'd-none' : ''}" role="status"><span class="sr-only">Loading...</span></div>
                  Download High Resolution
                  ${!badgeChecker() ? '<span class="badge badge-primary" style="background-color:#0071EB;" >Pro</span>' : ''}
                </a>
            </div>
          </div>
          `;
        }
      }
      outputContainer.innerHTML = htmlOutput;
      $('.img-container a').on('click', function(e){
        let self = this;
        let isNFSW = $(self).closest(".img-container").hasClass("nsfw");
        let isImageClicked = $(self).parent('.img-content').length;
        let fn = () => { };

        if(!isNFSW) return;
        if( isImageClicked ) {
          let isBlur = $(self).closest(".img-container").hasClass("blur-img");
          if(!isBlur) {
            $(self).closest(".img-container").addClass("blur-img"); // blur img
            return;
          }
          fn = () => {
            $(self).closest('.blur-img').removeClass('blur-img');
          }
        } else {
          // handle download button
          let btnAllowAttr = $(self).attr('allow');
          let btnHrefAttr = $(self).attr('href');
          let btnDownloadAttr = $(self).attr('download');
          let allowDownload = typeof btnAllowAttr !== 'undefined' && btnAllowAttr === "true";
          if(allowDownload || typeof btnHrefAttr === 'undefined' || typeof btnDownloadAttr === 'undefined') return;
          if(isLegalAge) return;
          fn = () => {
            $(self).attr("allow", "true");
          };
        }
        e.preventDefault();
        if(isLegalAge) {
          fn();
          return;
        }
        showAgeConfirmation(() => {
          isLegalAge = true;
          fn();
        });
      });
      let imageList = [];
      let imageData = {};
      const highResolutionButtons = document.querySelectorAll('.high-res');
      highResolutionButtons.forEach((button) => {
        const index = button.getAttribute('data-index');
        imageList.push({
          "data": `data:image/png;base64,${output.data.images[index]}`,
          "name": `dream-photo-hd-${[index]}.png`
        })
        imageData = {
          height: payload.height,
          width: payload.width
        }
      });
      if(isProChecker()){
        handleDownloadHighResolution(imageList, imageData);
      }
    }else{
      outputContainer.innerHTML = '<div class="alert alert-danger">'+ output.message +'</div>';
    }

    generateBtn.disabled = false;
  })
  .catch(error => {console.error(error)});



}

function isProChecker() {
  if(typeof AIUSER_PLAN !== 'undefined'){
    return AIUSER_PLAN && AIUSER_PLAN.toString().toLowerCase() !== 'basic';
  }
}

function showModal() {
  const modal = document.querySelectorAll('.modal');
  for (var i=0;i<modal.length;i+=1){
    modal[i].style.display = 'block';
  }
}

function redirectToUpgrade() {
  return window.location.href = "{{PAGE.btform.upgrade_url}}";
}

function badgeChecker(){
  setTimeout(() => {
    if(!isProChecker()){
      const proBadge = document.querySelectorAll('.badge');
      for (var i=0;i<proBadge.length;i+=1){
        proBadge[i].style.display = 'inline-block';
      }
    }
  },500)
}

function showTooltip(element) {
  // Trigger a mouseover event on the element to show the tooltip
  var event = new MouseEvent('mouseover', {
    bubbles: true,
    cancelable: true,
  });
  element.dispatchEvent(event);
}

</script>
{% endblock %}