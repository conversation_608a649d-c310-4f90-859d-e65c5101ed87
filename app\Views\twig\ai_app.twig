<!DOCTYPE html>
<html class="" lang="en">
	<head>

		{% include '_base_includes/head_meta.twig' %}

		{% include '_base_includes/head_gtag.twig' %}
		{% include '_base_includes/head_mixpanel.twig' %}

		<link rel="apple-touch-icon" sizes="180x180" href="{{ PAGE.favicons }}/apple-touch-icon.png">
		<link rel="icon" type="image/png" sizes="32x32" href="{{ PAGE.favicons }}/favicon-32x32.png">
		<link rel="icon" type="image/png" sizes="16x16" href="{{ PAGE.favicons }}/favicon-16x16.png">
		<link rel="manifest" href="{{ PAGE.favicons }}/site.webmanifest">
		<title>{{ PAGE.page_title }}</title>


		<link rel="preload" as="image" href="/images/loading-image.gif"/>
		<link rel="preload" as="image" href="/images/placeholder.svg"/>

		<script>
			const _grammar_ai_url = '{{ BASE.base_url }}api/openai/v1/text-completion2';
const OPENSOURCE_ENDPOINT = '{{ PAGE.OPENSOURCE_ENDPOINT }}';
if (OPENSOURCE_ENDPOINT) {
_grammar_ai_url = '{{ BASE.base_url }}api/opensource/v1/text-completion2';
}

window._grammar_ai_url = _grammar_ai_url;

window._grammar_ai_slug = '{{ PAGE.slug }}';
window._grammar_ai_surfToken = '{{ PAGE.surfToken.hash }}';{% if PAGE.btutil_auth_url != '' %}const ver = new Date().getTime();
const btutilAsset = document.createElement("script");
btutilAsset.setAttribute("src", "{{ PAGE.btutil_auth_url }}" + ver);
document.head.appendChild(btutilAsset);{% endif %}
		</script>

		<link rel="stylesheet" crossorigin href="/react_assets/index.css"/>
		<script type="module" crossorigin src="/react_assets/index.js"></script>
	</head>
	<body class="{{ PAGE.body_class }}">
		<div id="root"></div>
		<div id="modal-container" style="display: none">
			<div id="modal-content">
				<div id="modal-subtitle"></div>
				<h2 id="modal-title"></h2>
				<p id="modal-desc"></p>
				<a id="modal-cta" href="#" target="_parent"></a>
			</div>
		</div>
	</body>
</html>
