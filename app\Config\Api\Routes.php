<?php


/*
 * --------------------------------------------------------------------
 * Api Route Definitions
 * --------------------------------------------------------------------
 */

// $routes->post("api/ai/v1/test", "ArtificialIntelligence\ApiV1::test");

// $routes->post("api/ai/v1", "ArtificialIntelligence\ApiV1::v1");
$routes->post("api/ai/v1/(:any)", "ArtificialIntelligence\ApiOpenaiV1::v1/$1", ['filter' => "throttle"]);
$routes->post("api/openai/v1/(:any)", "ArtificialIntelligence\ApiOpenaiV1::v1/$1", ['filter' => "throttle"]);
$routes->post("api/opensource/v1/(:any)", "ArtificialIntelligence\ApiOpensourceV1::v1/$1", ['filter' => "throttle"]);

// $routes->post("api/ai/v2", "ArtificialIntelligence\ApiV2::v2");
$routes->post("api/ai/v2/(:any)", "ArtificialIntelligence\ApiV2::v2/$1");


//test mobile
$routes->post("api/openai/m/v1/(:any)", "ArtificialIntelligence\ApiOpenaiV1m::v1/$1", ['filter' => "throttle"]);



// AIPro Stable Diffusion
$routes->post("api/aipsd/v1/(:any)", "ArtificialIntelligence\ApiAiProStableDiffusionV1::v1/$1");
$routes->post("api/aipsd/v2/(:any)", "ArtificialIntelligence\ApiAiProStableDiffusionV1::v2/$1");