<?php

/**
 *  BT Utility
 */


/**
 *  Dev Debug Use
 */
function btutilDebug($_arr = [])
{
    echo '<pre>';
    print_r($_arr);
    echo '</pre>';
    die;
}


/**
 *  btutilGenerateUuid
 *  - PID Use
 *
 *  Returns 36 chars
 *  @return void
 */
function btutilGenerateUuid()
{
    return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
        uniqid(), mt_rand(0, 0xffff),
        mt_rand(0, 0xffff),
        mt_rand(0, 0x0fff) | 0x4000,
        mt_rand(0, 0x3fff) | 0x8000,
        mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
    );
}


/**
 *  btutilGenerateHashId
 *  - SessionID Use
 */
function btutilGenerateHashId($_key = [])
{
    $ctx = hash_init('fnv1a64');
    hash_update($ctx, $_key[0]);

    if (isset($_key[1])) {
        hash_update($ctx, $_key[1]);
    }

    if (isset($_key[2])) {
        hash_update($ctx, $_key[2]);
    }

    hash_update($ctx, time());
    return hash_final($ctx);
}

function btutilStripNonNumChars($_flag){

    return preg_replace('/[^0-9]/', '', $_flag);

}

// /**
//  *  Token Use
//  */
// function btutilGenerateUniqueId($_prefix = 'JUNE2020')
// {
//     return md5($_prefix . uniqid(rand(), true));
// }


// /**
//  *  Password Use //TODO: Janis - create password function with encryoption and decryption
//  */
// function btutilGeneratePassword($_value = '')
// {
//     $cost = 9;
//     return password_hash($_value, PASSWORD_BCRYPT, ['cost' => $cost]);
// }


// function btutilReplaceKey($_search = null, $_replace = null, $_subject = null)
// {
//     return str_replace('***|' . $_search . '|***', $_replace, $_subject);
// }

// function btutilAffixKey($_string)
// {
//     return '***|' . $_string . '|***';
// }


// function btutilEncrypt($_string, $_key = "Aug92020") {
//     return base64_encode($_key . $_string);
// }

// function btutilDecrypt($_string, $_key = "Aug92020") {
//     $withkey = base64_decode($_string);
//     return str_replace($_key, "", $withkey);;
// }

// //--------------------------------------------------------------------


// /**
//  *  Access Token Use
//  */
// function btutilGenerateAccessToken($_prefix = '4cc355')
// {
//     return btutilGenerateUniqueId($_prefix);
// }


// /**
//  *  Filename Token Use
//  */
// function btutilFilenameToken($_prefix = 'd0cf1l3')
// {
//     return btutilGenerateUniqueId($_prefix);
// }



// /**
//  *  URL Use
//  */
// function btutilUrlSafe($_string)
// {
//     return urldecode($_string);
// }



// /**
//  *  Encrypted Document Password Use
//  */
// function btutilEncryptDocumentPassword($_value)
// {
//     return btutilEncrypt($_value);
// }


// /**
//  *  Decrypted Document Password Use
//  */
// function btutilDecryptDocumentPassword($_value)
// {
//     return btutilDecrypt($_value);
// }


// /**
//  *  URL Use
//  */
// function btutilInputSafe($_string)
// {
//     return trim($_string);
// }