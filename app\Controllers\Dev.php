<?php namespace App\Controllers;

class Dev extends BaseController
{
    // public function index()
    // {
    //     echo '<pre>';

    //     $flows = [
    //         '01' => [
    //             ['lap' => '01'], //defined in constant/array in config as /theme/twig_file; flag('05', <default>)
    //             ['edt' => '01'],
    //             ['reg' => '01'],
    //             ['lgn' => '01'],
    //             ['pri' => '01'],
    //             ['pay' => '01'],
    //             ['typ' => '01'],
    //         ]
    //         // '01' => [
    //         //     'lp' => '01',
    //         //     'ed' => '01',
    //         //     'su' => '01',
    //         //     'pr' => '01',
    //         //     'pa' => '01',
    //         //     'ty' => '01'
    //         // ],
    //     ];

    //     $flow = $flows['01'][0];
    //     $flow['landpg'] = btflag('lp', $flow['landpg']);
    //     print_r($flow);
    //     echo '</pre>';
    // }

    public function debugClientSession()
    {
        // if (isset($_SESSION['surfToken'])) {
        //     echo '<pre>';
        //     echo 'SURF_TOKEN--> ';
        //     print_r($_SESSION['surfToken']);
        //     echo '</pre>';
        // }

        // if (isset($_SESSION['SLUG_ACCESS'])) {
		// 	echo '<pre>';
        // 	echo 'SLUG_ACCESS--> ';
        //     print_r($_SESSION['SLUG_ACCESS']);
		// 	echo '</pre>';
        // }

        // if (isset($_SESSION['AI_CHATBOT_SID'])) {
		// 	echo '<pre>';
        // 	echo 'AI_CHATBOT_SID--> ';
        //     print_r($_SESSION['AI_CHATBOT_SID']);
		// 	echo '</pre>';
        // }

        // if (isset($_SESSION['APPCHATBOT'])) {
			echo '<pre>';
        	echo 'SESSION --> ';
            print_r($this->session->get());
			echo '</pre>';
        // }









        // if (isset($_SESSION['AI_CHATBOT_USERPROMPT'])) {
		// 	echo '<pre>';
        // 	echo 'AI_CHATBOT_USERPROMPT--> ';
        //     print_r($_SESSION['AI_CHATBOT_USERPROMPT']);
		// 	echo '</pre>';
        // }

        // if (isset($_SESSION['AI_APP001_SID'])) {
		// 	echo '<pre>';
        // 	echo 'AI_APP001_SID [convert-to-proper-english] --> ';
        //     print_r($_SESSION['AI_APP001_SID']);
		// 	echo '</pre>';
        // }



        // echo '<pre>';
        // echo 'GUEST_USER: ';
        // print_r(btsessionGet('GUEST_USER'));
        // echo '</pre>';
        // echo '<pre>';
        // echo 'guest_document: ';
        // print_r(btsessionGet('guest_document'));
        // echo '</pre>';

        // echo '<pre>';
        // echo 'isLoggiedIn: ';
        // echo btsessionIsUserLoggedIn();
        // echo '</pre>';

        // echo '<pre>';
        // echo 'USER: ';
        // print_r(btsessionGet('USER'));
        // echo '</pre>';

        // echo '<pre>';
        // echo 'ACCOUNT_USER: ';
        // print_r(btsessionGet('ACCOUNT_USER'));
        // echo '</pre>';

        // echo '<pre>';
        // echo 'ACCOUNT: ';
        // print_r(btsessionGet('ACCOUNT'));
        // echo '</pre>';

        // echo '<pre>';
        // echo 'PLAN: ';
        // print_r(btsessionGet('PLAN'));
        // echo '</pre>';

        // echo '<pre>';
        // echo 'DOCUMENT: ';
        // print_r(btsessionGet('DOCUMENT'));
        // echo '</pre>';

        // echo '<pre>';
        // echo 'DOCUMENTTEMPLATE: ';
        // print_r(btsessionGet('DOCUMENTTEMPLATE'));
        // echo '</pre>';

        // echo '<pre>';
        // echo 'S4S_SIGNEE: ';
        // print_r(btsessionGet('S4S_SIGNEE'));
        // echo '</pre>';

        // echo '<pre>';
        // echo 'S4S_SIGNFIELD: ';
        // print_r(btsessionGet('S4S_SIGNFIELD'));
        // echo '</pre>';

        // echo '<pre>';
        // echo 'PASSWORD_ACCESS: ';
        // print_r(btsessionGet('PASSWORD_ACCESS'));
        // echo '</pre>';

        // echo '<pre>';
        // echo 'REDIRECT_ACTION: ';
        // print_r(btsessionGet('REDIRECT_ACTION'));
        // echo '</pre>';

        // echo '<pre>';
        // echo 'pid: ';
        // print_r(btsessionGet('pid'));
        // echo '</pre>';

        // echo '<pre>';
        // echo 'user_doc: ';
        // print_r(btsessionGet('user_doc'));
        // echo '</pre>';

        // echo '<pre>';
        // echo 'user_doc_update: ';
        // print_r(btsessionGet('user_doc_update'));
        // echo '</pre>';

    }

    public function testShell()
    {
        // echo shell_exec('php index.php dev testEmail > /dev/null 2>/dev/null &');
        // echo shell_exec('php index.php dev testEmail');
        // echo shell_exec('php index.php dev thisShell');
        // echo shell_exec('php spark app:info > /dev/null 2>/dev/null &');
        echo shell_exec('php spark app:info');
    }

    public function thisShell()
    {
        if (!is_cli()) {
            return $this->bcShow404();
        }
        echo "test";
    }

    public function thisShell2()
    {
        for ($x = 0; $x < 10000000; $x++) {
            echo $x;
        }
    }

    // public function testEmail() {
    //     // btemailSend();
    //     btemailTest();
    // }

    // public function show404() {
    //     // To activate flag specific page
    //     $theme = btflag('theme', BTBASETHEME_DEF);

    //     $this->view_data['_'] = ['id' => 'er'];
    //     $this->view_data['login'] = btsessionIsUserLoggedIn();
    //     $this->view_data['meta_data'] = [
    //         ['name' => 'description', 'content' => '404'],
    //     ];
    //     $this->view_data['page'] = ['title' => '404'];

    //     switch ($theme) {
    //     default:
    //         $this->dagger();
    //     }
    // }

    // public function email01() {
    //     echo '<HTML>

    //     <HEAD>

    //     <TITLE>Your Title Here</TITLE>

    //     </HEAD>

    //     <BODY BGCOLOR="000">

    //     <CENTER><IMG SRC="clouds.jpg" ALIGN="BOTTOM"> </CENTER>

    //     <HR>

    //     <a href="http://somegreatsite.com">Link Name</a>

    //     is a link to another nifty site

    //     <H1>This is a Header</H1>

    //     <H2>This is a Medium Header</H2>

    //     Send me mail at <a href="mailto:<EMAIL>">

    //     <EMAIL></a>.

    //     <P> This is a new paragraph!

    //     <P> <B>This is a new paragraph!</B>

    //     <BR> <B><I>This is a new sentence without a paragraph break, in bold italics.</I></B>

    //     <HR>

    //     </BODY>

    //     </HTML>';
    // }

    // private function dagger() {
    //     $data = $this->view_data;

    //     $this->view_data = $data;
    //     $this->bcTwigRender('er-bootstrap-dagger');
    // }

    //--------------------------------------------------------------------

}
