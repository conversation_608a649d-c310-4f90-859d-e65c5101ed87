import { useEffect, useState } from "react";
import { setCookie, getCookie } from "../utils/cookie";

export default function PricingPopup({
  isVisible = false,
  setIsVisible,
  appName = "",
  app = "",
}: {
  isVisible: boolean;
  setIsVisible: (isVisible: boolean) => void;
  appName?: string;
  app?: string;
}) {
  setCookie("app", app, { domain: ".ai-pro.org" });

  const [slug, setSlug] = useState("register-auth");
  const [modalStyles, setModalStyles] = useState("");

  useEffect(() => {
    const checkEmail = () => {
      if (getCookie("user_email")) {
        setSlug("pricing");
      } else {
        setSlug("register-auth");
      }
    };

    checkEmail();
    const interval = setInterval(applyStyle, 300);
    return () => clearInterval(interval);
  }, []);
  const applyStyle = () => {
    if (app === "pro") {
      const style = !getCookie("user_email")
        ? "h-[85%] max-h-720:h-[94%] xl:h-[70%] min-w-2560:h-[50%] min-w-[26%] md:min-w-[30%] md:w-full md:max-w-[62%] lg:max-w-[50%] xl:max-w-[36%] min-w-2560:max-w-[28%] min-w-2560:min-w-[26%] transition-all delay-150 duration-300 ease-in-out"
        : "transition-all delay-150 duration-300 ease-in-out md:min-w-[710px] md:w-[68%] md:max-w-[68%] max-w-768-1280:h-[90%] h-[85%]";
      setModalStyles(style);
      return;
    }
    const width =
      "md:min-w-[710px] sm:w-[95%] sm:max-w-[95%] lg:w-[88%] lg:max-w-[88%] min-w-1440:w-[68%] min-w-1440:max-w-[68%] max-w-768-1280:h-[90%]";

    const style = !getCookie("user_email")
      ? "min-w-[26%] h-[70%] md:min-w-[30%] md:w-full md:max-w-[62%] max-h-720:h-[94%] lg:max-w-[50%] xl:max-w-[36%] xl:h-[70%] min-w-2560:max-w-[28%] min-w-2560:min-w-[26%] min-w-2560:h-[50%] transition-all delay-150 duration-300 ease-in-out"
      : `transition-all delay-150 duration-300 ease-in-out ${width} h-[85%]`;
    setModalStyles(style);
  };

  const startUrl = import.meta.env.VITE_START_URL || "https://start.ai-pro.org";

  return (
    <div
      id="iframe-pricing"
      className={`fixed inset-0 flex items-center justify-center bg-black/50 duration-500 ${!isVisible ? "z-[0] hidden" : "z-[10000]"}`}
    >
      <div
        className={`relative my-auto w-[90%] rounded-3xl ${modalStyles} transition-all duration-500`}
      >
        <iframe
          src={`${startUrl}/${slug}?appName=${appName}`}
          title="Register Authentication"
          frameBorder="0"
          className="h-full w-full rounded-3xl"
        />
        <svg
          onClick={() => {
            setIsVisible(false);
          }}
          stroke="currentColor"
          fill="currentColor"
          strokeWidth="0"
          viewBox="0 0 24 24"
          className="absolute top-4 right-4 h-5 w-5 cursor-pointer text-gray-600"
          height="1em"
          width="1em"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path fill="none" d="M0 0h24v24H0V0z" opacity=".87"></path>
          <path d="M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm3.59-13L12 10.59 8.41 7 7 8.41 10.59 12 7 15.59 8.41 17 12 13.41 15.59 17 17 15.59 13.41 12 17 8.41z"></path>
        </svg>
      </div>
    </div>
  );
}
