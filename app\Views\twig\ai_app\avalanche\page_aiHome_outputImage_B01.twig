{% extends 'base_A_template.twig' %}

{% block head_styles %}
{{ parent() }}
<style>
  #createArtcontainer {
    text-align: center;
    margin: 20px auto;
  }

  #positive-prompt, #negative-prompt {text-align:left !important;}
  .btn.btn-success.btn-md.btn-block {font-weight: bold;}
  ::-webkit-input-placeholder {opacity: 0.5 !important;}
  ::-moz-placeholder {opacity: 0.5 !important;}
  :-ms-placeholder {opacity: 0.5 !important;}
  ::placeholder {opacity: 0.5 !important}

  .suggestion-container p{
    font-size: 12px;
    font-weight: bold;
  }

  .suggestion { margin-top: 25px }

  .suggestion-items-container { text-align: initial; }

  .item{
    display: inline-block;
    padding: 2px 15px;
    font-size: 12px;
    border: 1px solid #6D6D6D;
    border-radius: 15px;
    color: #6D6D6D;
    margin: 0 5px 7px 0;
    cursor: pointer;
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* IE10+/Edge */
    user-select: none; /* Standard */
  }

  .item.active{
    color: #fff;
    background: #6D6D6D;
  }

  .suggestion-container.container,
  .suggestion-container .col-md-3 {
    padding-left: 0;
  }

  @media only screen and (min-width: 769px) {
    #createArtcontainer {width:800px}
  }

  @media (max-width: 769px) {
    .suggestion-container {float: left; padding-left:15px !important;}
    .suggestion-container p{
      text-align: left;
    }
  }
</style>
<link rel='stylesheet' href='https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css'>
<link rel='stylesheet' href='https://cdn.form.io/formiojs/formio.full.min.css'>
{% endblock %}


{% block head_scripts %}
{{ parent() }}
<script>
  let ver = new Date().getTime();
  let btutilAsset = document.createElement('script');
  btutilAsset.setAttribute('src', '{{ PAGE.btutil_auth_url }}' + ver);
  document.head.appendChild(btutilAsset);
</script>
<script src='https://cdn.form.io/formiojs/formio.full.min.js?ver={{ BASE.build }}'></script>
<script type='text/javascript'>
  window.onload = function () {
    var force = 1;
    var builder_template = {
  "label": "Container",
  "tableView": false,
  "key": "container",
  "type": "container",
  "input": true,
  "components": [
    {
      "label": "HTML",
      "tag": "h1",
      "attrs": [
        {
          "attr": "",
          "value": ""
        }
      ],
      "content": "{{ PAGE.btform.page_title }}",
      "refreshOnChange": false,
      "key": "html",
      "type": "htmlelement",
      "input": false,
      "tableView": false
    },
    {
      "key": "fieldSet",
      "type": "fieldset",
      "label": "Field Set",
      "input": false,
      "tableView": false,
      "components": [
        {
          "label": "<b>Positive prompt:</b>{{ PAGE.btform.inputPositivePrompt_label }}",
          "placeholder": "A really cute cat, running through the snow, wearing pink shoes",
          "autoExpand": false,
          "tableView": true,
          "key": "positivePromptArea",
          "type": "textarea",
          "input": true,
          "id": "positive-prompt"
        },
        {
          "label": "<b>Negative prompt:</b>{{ PAGE.btform.inputNegativePrompt_label }}",
          "placeholder": "Extra limbs, extra fingers, weird eyes, fangs, and alien-looking body",
          "autoExpand": false,
          "tableView": true,
          "key": "negativePromptArea",
          "type": "textarea",
          "input": true,
          "id": "negative-prompt"
        },
        {
          "label": "{{ PAGE.btform.submit_label }}",
          "action": "custom",
          "showValidations": true,
          "theme": "success",
          "block": true,
          "shortcut": "Enter",
          "disableOnInvalid": true,
          "tableView": false,
          "key": "submit",
          "type": "button",
          "custom": "generate();",
          "input": true
        },
        {
          "label": "HTML",
          "attrs": [
            {
              "attr": "id",
              "value": "outputProperEnglish"
            }
          ],
          "refreshOnChange": false,
          "key": "htmlOutput",
          "type": "htmlelement",
          "input": false,
          "tableView": false
        }
      ]
    }
  ]
}

    builder_template = JSON.stringify(builder_template);

    Formio.createForm(document.getElementById('formio'), JSON.parse(builder_template), {}).then(function (form) {
      form.on("change", function (e) {
        //
      });
    });

    const positivePrompt = document.querySelector('textarea[id*="-positivePromptArea"]');
    const newDiv = document.createElement("div");
    newDiv.classList.add('suggestion');

    newDiv.innerHTML = '<div class="suggestion-container container"><div class="row"><div class="col-md-3"><p>Suggested Prompts for:</p></div><div class="suggestion-items-container col-md-9"><div class="sugg-1 item">Portrait</div><div class="sugg-2 item">Scenic View</div><div class="sugg-3 item">Food</div><div class="sugg-4 item">Animal</div><div class="sugg-5 item">Vehicle</div><div class="sugg-6 item">Architecture</div><div class="sugg-7 item">Monster</div><div class="sugg-8 item">Anime</div></div></div></div>';
    positivePrompt.insertAdjacentElement("afterend", newDiv);

  }
</script>
{% endblock %}



{% block body_content %}
{{ parent() }}
<div style="padding: 50px" id="createArtcontainer">
    <div id='formio'></div>
</div>
{% endblock %}


{% block body_footer %}
{{ parent() }}
{% endblock %}



{% block body_hidden_content %}
{{ parent() }}
{% endblock %}


{% block body_scripts %}
{{ parent() }}
<script src="{{ BASE.assets_common }}js/jquery-3.5.1.min.js?ver={{ BASE.build }}"></script>

<script type='text/javascript'>
  function generate() {
    if ($('.btn, .btn-success').prop('disabled')) {
      return;
    }

    var url = '{{ BASE.base_url }}api/aipsd/v1/create-art';

    var slug = '{{ PAGE.slug }}';
    var surfToken = '{{ PAGE.surfToken.hash }}';
    var positivePrompt = document.querySelector('textarea[id*="-positivePromptArea"]');
    var negativePrompt = document.querySelector('textarea[id*="-negativePromptArea"]');
    var outputContainer = document.getElementById('outputProperEnglish');
    outputContainer.innerHTML = '<div class="d-flex justify-content-center"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>';
    var formData = new FormData();
    var prompt = ({positive: positivePrompt.value, negative: negativePrompt.value});
    formData.append('prompt', JSON.stringify(prompt));
    formData.append('slug', slug);
    formData.append('surfToken', surfToken);

    fetch(url, { method: 'POST', body: formData })
    .then(response => {
      return response.json();
    })
    .then((output) => {
      if (output.success) {
        // console.log(output.data.images);
        var htmlOutput = '';
        for (var i = 0; i < output.data.images.length; i++) {
            htmlOutput += "<img src='data:image/png;base64, " + output.data.images[i] + "' />";
        }
        outputContainer.innerHTML = htmlOutput;

        $('#outputProperEnglish').children('img').each(function () {
          $(this).addClass('img-fluid');
          $(this).after('<div class="mt-2 mb-3 col-xs-1" align="center"><a style="max-width:512px;" href="'+this.getAttribute('src')+'" download="download" class="btn btn-primary btn-md btn-block active" role="button" aria-pressed="true">DOWNLOAD</a><hr></div>');
        });

      } else {
        outputContainer.innerHTML = '<div class="alert alert-danger">'+ output.message +'</div>';
      }
    })
    .catch(error => {
      // handle the error
      console.error(error);
    });
    
    //trigger when user clicks Generate Image button
    //TrustPilot logic change
    const TPLogicRun = window.TPLogicRun;
    console.log("TPLogicRun", typeof TPLogicRun);
    if (typeof TPLogicRun === 'function') {
      TPLogicRun();
    } else {
      console.error("TrustPilot TPLogicRun function failed to execute");
    }
  }

  $(document).ready(function () {
      document.addEventListener('click', function(event) {
        const positivePrompt = document.querySelector('textarea[id*="-positivePromptArea"]');
        const negativePrompt = document.querySelector('textarea[id*="-negativePromptArea"]');
        const target = event.target;
        if (target.classList.contains('item')) {
          const itemText = target.innerText;
          const suggestionData = {
            'items': [
              {
                'text': 'Portrait',
                'positivePrompt': [
                  'Close up portrait of a white-haired old man, Exquisite detail, 30-megapixel, 4k, 85-mm-lens, sharp-focus, f:8, ISO 100, shutter-speed 1:125, diffuse-back-lighting, award-winning photograph, small-catchlight, High-sharpness, facial-symmetry',
                  'Closeup portrait of a cyborg, mechanical parts, ultra realistic, concept art, intricate details, eerie, highly detailed, photorealistic, 8k, unreal engine. art by artgerm and greg rutkowski and charlie bowater and magali villeneuve and alphonse mucha, golden hour, cyberpunk, robotic, steampunk, neon colors, metallic textures.',
                  'Professional portrait photograph of a gorgeous Norwegian girl in winter clothing with long wavy blonde hair, ((sultry flirty look)), freckles, beautiful symmetrical face, cute natural makeup, ((standing outside in snowy city street)), stunning modern urban upscale environment, ultra realistic, concept art, elegant, highly detailed, intricate, sharp focus, depth of field, f/1. 8, 85mm, medium shot, mid shot, (centered image composition), (professionally color graded), ((bright soft diffused light)), volumetric fog, trending on instagram, trending on tumblr, hdr 4k, 8k'
                ],
                'negativePrompt':[
                  '(bonnet), (hat), (beanie), cap, (((wide shot))), (cropped head), bad framing, out of frame, deformed, cripple, old, fat, ugly, poor, missing arm, additional arms, additional legs, additional head, additional face, multiple people, group of people, dyed hair, black and white, grayscale'
                ]
              },
              {
                'text': 'Scenic View',
                'positivePrompt': [
                  'View of the northern lights at night time, seen in Alaska, Canon RF 16mm f:2.8 STM Lens, hyperrealistic photography, style of Unsplash and National Geographic',
                  'Sea of Endless waves with the sparkling reflection of the sun, golden hour, Canon RF 16mm f:2.8 STM Lens,  hyperrealistic photography, style of unsplash and National Geographic',
                  'Hills of the Scotland highlands, misty fog, Canon RF 16mm f:2.8 STM Lens, award winning photography, by National Geographic and Unsplash'
                ],
              },
              {
                'text': 'Food',
                'positivePrompt': [
                  'sliced ​​rib-eye steak on a red plate, with separate stir-fry vegetables and extra fried potatoes in a bowl, on a steel table + 4k resolution + photorealistic, ambient light  --beta --upbeta',
                  'very healthy food plate with avocados, tomatoes, eggs and other healthy food, 8 k resolution, food photography, studio lighting, sharp focus, hyper - detailed',
                  'royal fast food, burger, fries, coffee, nuggets, salad, steam coming out, hyper detailed, intricate photorealistic, 8k, UHD, delicious, amazing, epic, epic contour light, volumetric light --v 4 --upbeta'
                ],
              },
              {
                'text': 'Animal',
                'positivePrompt': [
                  'majestic Lion, mane made of fire, illuminating the surrounding area creating beautiful stunning shadows around it and over the Lion’s head.  painted portrait ,fantasy, intricate, elegant, highly detailed, digital painting, artstation, concept art, smooth, sharp focus, illustration, art by gaston bussiere and alphonse mucha',
                  'photography print of taxidermy Whitehead Eagle, spread wings, majestic, stuffing of twigs and moss falling out, next on a cliff background, dappled lighting, backlit, by ellen jewett and Dariusz Zawadzki and Brian froud and tom bagshaw, real, realistic, 8k, high resolution, high definition, wildlife photography --ar 5:3 --s 3500',
                  'a shark themed mecha walking on the water, diffuse lighting, fantasy, highly detailed, photorealistic, digital painting, artstation, illustration, concept art, smooth, sharp focus'
                ],
              },
              {
                'text': 'Vehicle',
                'positivePrompt': [
                  'concept art of motorcycle, highly detailed painting by dustin nguyen, akihiko yoshida, greg tocchini, greg rutkowski, cliff chiang, 4 k resolution, trending on artstation, 8k',
                  'a concept spaceship made out of Tesla parts, Ferrari parts, F1 racing parts :: x-wing, concept art, art station trends, airbase setting, space station setting, unreal engine render, octane render, 4k, volumetric lighting, photorealistic, new type, sleek design, ultra aerodynamic, spacecraft, dynamic style, F-22 fighter jet, concept car, stealth bomber, wide angle --ar 5:3',
                  'sport car, shimmery metallic blue job paint, aggressive look, tuned, bmw and corvette mix, full car only, in motion, (bright headlights on:1.6), (at night:1.6), high speed, (motion blur:1.3), (driver:1.6), movie action scene, (Need for Speed:1.3), wet road, illegal racing game, (industrial cityscape in background:1.5), (detailed stunning environment:1.5), (foggy), moody dark atmosphere, bright headlights, neon underground aesthetics, (sci-fi), cyberpunk, blade runner, cinematic, cover art, (low front angle), full view of a sports car, intricate, highly detailed, digital painting, digital art, artstation, concept art, (complementary colors:1.5), (color contrast:1.5), best quality masterpiece, photorealistic, detailed, sharp focus, 8k, HDR, shallow depth of field, broad light, high contrast, backlighting, bloom, light sparkles, chromatic aberration, sharp focus, RAW color photo'
                ],
              },
              {
                'text': 'Architecture',
                'positivePrompt': [
                  'house, golden hour, Canon RF 16mm f:2.8 STM Lens,  hyperrealistic photography, style of unsplash and National Geographic',
                  'a lush apartment building covered in vines by studio ghibli sticker:: a die cut studio ghibli sticker of an apartment building in a lush jungle city --ar 9:16',
                  'A man smoking in the balcony of his condominium surrounded by windows with lush vegetation, wooden floor, high ceiling, beige blue salmon pastel palette, interior design magazine, cozy atmosphere'
                ],
              },
              {
                'text': 'Monster',
                'positivePrompt': [
                  'Closeup portrait of a monster with glowing eyes and sharp teeth, dark shadows, foggy background, highly detailed, photorealism, concept art, digital painting, art by yahoo kim, max grecke, james white, viktor hulík, fabrizio bortolussi.',
                  'a humanoid monster made of metal and flensed muscle , flensed muscle, gears, monster concept art, monster art, --c 100 --ar 3:4',
                  'A seamless pattern of surreal colorful fluffy elaborate and detailed futuristic monsters, complicated surreal monsters, fun patterns, detailed accents, monster details, colorful --v4'
                ],
              },
              {
                'text': 'Anime',
                'positivePrompt': [
                  'game 2d art, image of a young man with light gray hair, 16 years old, green eyes, white tank top, steampunk clothes, anime, anime anime anime anime anime, dynamic pose, ultra-detailed, white background, HD, design art by Hideo Minaba, Yuya Nagai, Ryoji Ohara, Ryota Murayama and Hitomi Yoshimura, granblue fantasy --v4',
                  'game 2d art, image of a young girl with light blonde hair, 14 years old, green eyes, white skirt, anime, anime anime anime anime anime, dynamic pose, ultra-detailed, white background, HD, design art by Hideo Minaba, Yuya Nagai, Ryoji Ohara, Ryota Murayama and Hitomi Yoshimura, granblue fantasy, artgerm --v4'
                ],
              },
            ]
          }
          const suggestion = suggestionData.items.find(item => item.text === itemText);

          if (suggestion) {
            const positivePrompts = suggestion.positivePrompt;
            const negativePrompts = suggestion.negativePrompt || [];
            
            const randomPositivePrompt = positivePrompts[Math.floor(Math.random() * positivePrompts.length)];
            const randomNegativePrompt = negativePrompts.length > 0 ? negativePrompts[Math.floor(Math.random() * negativePrompts.length)] : '';
            const activeItems = document.querySelectorAll('.item.active');
            activeItems.forEach(function(item) {
              item.classList.remove('active');
            });

            target.setAttribute('data-positive-prompt', randomPositivePrompt);
            target.setAttribute('data-negative-prompt', randomNegativePrompt);

            const positivePrompt = target.getAttribute('data-positive-prompt');
            const negativePrompt = target.getAttribute('data-negative-prompt');
          }

          if (target.classList.contains('active')){
            positivePrompt.value = '';
            negativePrompt.value = '';
          } else {
            target.classList.add("active");
            positivePrompt.value = target.getAttribute('data-positive-prompt');
            negativePrompt.value = target.getAttribute('data-negative-prompt');
          }

          
        }
      });

      document.addEventListener('input', function(evt) {
        if ((evt.target.matches('textarea[id*="-positivePromptArea"]')) && (evt.target.value === '')){
          const activeItems = document.querySelectorAll('.item.active');
          activeItems.forEach(function(item){
            item.classList.remove('active');
          });
        } 
      });
  });

</script>
{% endblock %}