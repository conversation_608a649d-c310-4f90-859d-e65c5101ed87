{% extends 'base_B_template.twig' %}

{% block head_styles %}
{{ parent() }}
<style>
  .export-modal {
    font-family: "Poppins", sans-serif;
  }

  .save-a-copy h1 {
    font-size: 2rem !important;
    font-weight: 500 !important;
  }

  .export-to-pdf, .go-back {
    font-weight: 600 !important;
  }

  .regen {
    font-size: 12px;
    cursor: pointer;
    border: 0.5px solid #a3e195;
    padding: 2px;
    float: right;
    position: absolute;
    bottom: 13px;
    right: 13px;
  }

  #outputData {
    padding: 2.75rem 1.25rem;
    position: relative;
  }

  #gramAicontainer {
    text-align: center;
    display: flex;
    flex-direction: column;
    margin: 20px auto;
  }


  button#exportToPdf, button#exportToPdf:hover, button#copyToClip, button#copyToClip:hover {
    background-color: #28a745;
    border: 1px solid #28a745;
    border-color: #28a745;
    color: #fff;
    width: 200px;
  }

  @media only screen and (max-width: 767px) {
    button#copyToClip, button#exportToPdf {
      margin-bottom: 1rem!important;
    }
  }

  @media only screen and (min-width: 768px) {
      #gramAicontainer {
        width:800px;
      }
      button#exportToPdf {
        margin-right: 0.5rem!important;
        margin-top: 0;
      }
  }

#gramAidisclaimer {
    font-size: 12px;
    text-align: center;
    padding-top: 2px;
    padding-bottom: 20px;
    padding-left: 3px;
    padding-right: 3px;
    color: rgba(0,0,0,0.5);
    text-decoration: underline;
    margin-top: auto;
}

#gramAidisclaimer a {
    color: inherit;
}

 @media only screen and (max-height: 479px) {
    #gramAidisclaimer {
      display: none;
    }
  }


</style>
<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap">
<link rel='stylesheet' href='//stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css?ver={{ BASE.build }}'>
<link rel='stylesheet' href='https://cdn.form.io/formiojs/formio.full.min.css?ver={{ BASE.build }}'>
{% endblock %}


{% block head_scripts %}
{{ parent() }}
<script>
  let ver = new Date().getTime();
  let btutilAsset = document.createElement('script');
  btutilAsset.setAttribute('src', '{{ PAGE.btutil_auth_url }}' + ver);
  document.head.appendChild(btutilAsset);
</script>
<script src='https://cdn.form.io/formiojs/formio.full.min.js?ver={{ BASE.build }}'></script>
<script type='text/javascript'>
  window.onload = function () {
    var force = 1;
    var builder_template =
    {
      "label": "Container",
      "tableView": false,
      "key": "container",
      "type": "container",
      "input": true,
      "components": [
        {
          "label": "HTML",
          "tag": "h1",
          "attrs": [
            {
              "attr": "",
              "value": ""
            }
          ],
          "tabindex": 0,
          "content": "{{ PAGE.btform.page_title }}",
          "refreshOnChange": false,
          "key": "html",
          "type": "htmlelement",
          "input": false,
          "tableView": false
        },
        {
          "key": "fieldSet",
          "type": "fieldset",
          "label": "Field Set",
          "input": false,
          "tableView": false,
          "components": [
            {
              "tabindex": 0,
              "label": "{{ PAGE.btform.textfield_label }}",
              "autoExpand": false,
              "showWordCount": true,
              "showCharCount": true,
              "tableView": true,
              "key": "convertArea",
              "id": "textarea",
              "type": "textarea",
              "input": true
            },
            {
              "tabindex": 0,
              "label": "{{ PAGE.btform.submit_label }}",
              "action": "custom",
              "showValidations": true,
              "theme": "success",
              "block": true,
              "disableOnInvalid": true,
              "tableView": false,
              "key": "submit",
              "type": "button",
              "input": true,
              "attributes": {
                "type": "button",
                "style": "font-weight: bold;"
              },
              "custom": "generate();"
            },
            {
              "label": "HTML",
              "attrs": [
                {
                  "attr": "id",
                  "value": "outputProperEnglish"
                }
              ],
              "refreshOnChange": false,
              "key": "htmlOutput",
              "type": "htmlelement",
              "input": false,
              "tableView": false
            },
             {
              "label": "HTML",
              "attrs": [
                {
                  "attr": "id",
                  "value": "buttonContainer"
                }
              ],
              "content": "",
              "refreshOnChange": false,
              "key": "htmlButtonContainer",
              "type": "htmlelement",
              "input": false,
              "tableView": false
            }
          ]
        }
      ]
    }

    builder_template = JSON.stringify(builder_template);

    Formio.createForm(document.getElementById('formio'), JSON.parse(builder_template), {}).then(function (form) {
      form.on("change", function (e) {
        //
      });
    });

  }

</script>
{% endblock %}




{% block body_content %}
{{ parent() }}
  <div style="padding:50px; height: 100vh;" id="gramAicontainer">
    <div id='formio'></div>
    <footer id="gramAidisclaimer">
        <a href="https://ai-pro.org/disclaimer/" target="_blank" rel="noreferrer">We are neither affiliated nor related to OpenAI.</a>
    </footer>
  </div>
    <div id="modal-container" style="display: none">
    <div id="modal-content">
      <div id="modal-subtitle"></div>
      <h2 id="modal-title"></h2>
      <p id="modal-desc"></p>
      <a id="modal-cta" href="#" target="_parent"></a>
    </div>
  </div>

  {% if PAGE.flagShowUsage %}<div id="usageCounter"></div>{% endif %}
{% endblock %}



{% block body_hidden_content %}
{{ parent() }}
{% endblock %}


{% block body_scripts %}
{{ parent() }}
<script src="{{ BASE.assets_common }}js/jquery-3.5.1.min.js?ver={{ BASE.build }}"></script>

<script type='text/javascript'>

  var cbSubmit = generate;
  var regenerateBtn = document.querySelectorAll('.regen');

  $(document).on('click', (el) => {
    if(el.target.classList.contains('regen')){
      generate();
    }
  })

  $(document).on('keyup', 'textarea, .regen', function(event) {
    if (event.keyCode === 13) {
      generate();
    }
  });


  function copyToClipboard(){
    const copyText  = document.getElementById('outputDataText');
    let formattedText = copyText.innerHTML;
    formattedText = formattedText.replace(/<br\s*\/?>/gi, '\n');
    const tempTextarea = document.createElement('textarea');
    tempTextarea.value = formattedText;
    document.body.appendChild(tempTextarea);
    tempTextarea.select();
    tempTextarea.setSelectionRange(0, 99999);
    document.execCommand('copy');
    document.body.removeChild(tempTextarea);
    alert('Text copied to clipboard!');
  }

  // Initialize an array to store prompt and output pairs
  let promptAndOutput = [];

  // Function to add a prompt and output pair
  function addPromptAndOutput(englishOutput) {
    let pair = {
      properEnglish: englishOutput
    };
    promptAndOutput.push(pair);
  }

  //hidden div container for export to PDF
  let response_to_export_div = document.createElement("div");
  response_to_export_div.classList.add("response-to-export");
  response_to_export_div.style.display = "none";

  const responseToExport = () => {
    const response_to_export = document.querySelector(".response-to-export");
    return response_to_export ? response_to_export.textContent : "";
  };

  const onClickExportToPDF = () => {
    const response = responseToExport();
    let filename = "export-proper-english";
    generatePDF(response, filename);
  };

  const onClickExportButton = () => {
    let modal = document.querySelector(".export-modal-container");

    if (!modal) {
      const btutil_buildExportModal = window.btutil_buildExportModal;

      modal = btutil_buildExportModal(onClickExportToPDF);
      document.body.appendChild(modal);
    }
    modal.classList.add("active");
    const h1Element = modal.querySelector("h1");
    if (h1Element) {
      h1Element.setAttribute("aria-label", "Save a Copy");
    }
  };

  // Function to update an existing key-value pair or add a new one
  function updatePromptAndOutput(newEnglishOutput) {
    let updated = false; // A flag to track if the update was performed

    if (!updated) {
      // If the pair doesn't exist, add a new key-value pair
      addPromptAndOutput(newEnglishOutput);
    }
  }

  // Function to append key-value pairs to response_to_export_div
  function appendKeyValuesToResponseDiv() {
    // Clear the existing content of response_to_export_div
    response_to_export_div.innerHTML = '';

    // Iterate through promptAndOutput and add key-value pairs to response_to_export_div
    promptAndOutput.forEach(function(dataObject) {
      let objectDiv = document.createElement("div");
      objectDiv.style.fontFamily = 'Times New Roman';
      objectDiv.style.textAlign = "left";
      // Iterate over the key-value pairs and create p elements
      for (const key of Object.keys(dataObject)) {
        if (dataObject.hasOwnProperty(key)) {
          const pElement = document.createElement("p");
          pElement.textContent = dataObject[key];
          objectDiv.appendChild(pElement);
        }
      }
      response_to_export_div.appendChild(objectDiv);
    });
    document.body.appendChild(response_to_export_div);
  }

  function setupDisclaimer(outputData) {
  insertGramAiDisclaimer(outputData);
  window.addEventListener('resize', () => insertGramAiDisclaimer(outputData));
}



  async function is_limited(apiGetUsageUrl) {
    if (IS_FREE_USER) {
      try {
        const data = await btutil_getUsageV2(apiGetUsageUrl);
        TRIED_USAGE = data.usage;
      } catch (e) {
        console.error(e);
      }
      if (TRIED_USAGE >= AIPRO_USER.usage_limit) {
        btutil_modalRegisterUpgrade();
        return true;
      }
    }

    return false;
  }


  async function generate() {
    if ($('.btn, .btn-success').prop('disabled')) {
      return;
    }
    
    var url = '{{ BASE.base_url }}api/openai/v1/text-completion2';
    const OPENSOURCE_ENDPOINT = '{{ PAGE.OPENSOURCE_ENDPOINT }}';
    if(OPENSOURCE_ENDPOINT){
      url = '{{ BASE.base_url }}api/opensource/v1/text-completion2';
    }

    var slug = '{{ PAGE.slug }}';
    var txtPrompt = document.querySelector('textarea[id*="-convertArea"]');
    var surfToken = '{{ PAGE.surfToken.hash }}';
    var outputContainer = document.getElementById('outputProperEnglish');
    outputContainer.innerHTML = '<div class="d-flex justify-content-center"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>';
    var usageCounter = document.getElementById('usageCounter');
    var formData = new FormData();

    formData.append('prompt', txtPrompt.value);
    formData.append('slug', slug);
    formData.append('surfToken', surfToken);
    formData.append('isParagraph', isParagraph(txtPrompt.value));

    fetch(url, { method: 'POST', body: formData })
    .then(response => {
      if(!response.ok){
          outputContainer.innerHTML = '<div class="alert alert-primary">'+ response.statusText +'</div>';
          return;
      }
      return response.json();
    })
    .then((output) => {
      if(output.status === "invalid") {
        btutil_modalRegisterUpgrade();
      } else if(output.status === "max-tokens") {
        if(output.ent_member && output.ent_member == "yes") {
          btutil_modalMaxTokenUpgradeEntMembers();
          return;
        }
        btutil_modalMaxTokenUpgrade();
      }
      if (output.success == 2) {
        const modalsubTitle = document.querySelector('#modal-subtitle');
        const modalTitle = document.querySelector('#modal-title');
        const modalDesc = document.querySelector('#modal-desc');
        const modalCta = document.querySelector('#modal-cta');

        modalsubTitle.innerHTML = output.modal.subTitle;
        modalTitle.innerHTML = output.modal.title;
        modalDesc.innerHTML = output.modal.desc;
        modalCta.innerHTML = output.modal.ctaLabel;
        modalCta.href = output.modal.redirectUrl;

        const modalContainer = document.querySelector('#modal-container');
        modalContainer.style.display = 'block';
        return;
      }
      if (output.success) {
        outputContainer.innerHTML = '<div class="alert alert-success ' + (isParagraph(txtPrompt.value) ? 'text-left' : 'text-center') + '" id="outputData"><div id="outputDataText">' + output.data + '</div><span class="text-right regen" aria-label="regenerate" tabIndex="0" role="button">Regenerate <svg aria-label="regenerate" role="img" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-counterclockwise" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M8 3a5 5 0 1 1-4.546 2.914.5.5 0 0 0-.908-.417A6 6 0 1 0 8 2z"></path><path d="M8 4.466V.534a.25.25 0 0 0-.41-.192L5.23 2.308a.25.25 0 0 0 0 .384l2.36 1.966A.25.25 0 0 0 8 4.466"></path></svg></span> </div>';
        // start of export to PDF logic
        if(txtPrompt.value){
          // Display both buttons in the same div
          let buttonContainer = document.getElementById("buttonContainer");
          buttonContainer.innerHTML = `
          <button aria-label="export result" id='exportToPdf' tabIndex="0" class='btn btn-primary btn-md exportToPdf' onclick='onClickExportButton();' type='button'>
              <svg aria-label="export icon" role="img" height='1em' fill='#ffffff' viewBox='0 0 512 512'>
                  <path d='M216 0h80c13.3 0 24 10.7 24 24v168h87.7c17.8 0 26.7 21.5 14.1 34.1L269.7 378.3c-7.5 7.5-19.8 7.5-27.3 0L90.1 226.1c-12.6-12.6-3.7-34.1 14.1-34.1H192V24c0-13.3 10.7-24 24-24zm296 376v112c0 13.3-10.7 24-24 24H24c-13.3 0-24-10.7-24-24V376c0-13.3 10.7-24 24-24h146.7l49 49c20.1 20.1 52.5 20.1 72.6 0l49-49H488c13.3 0 24 10.7 24 24zm-124 88c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20zm64 0c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20z'>
                  </path>
              </svg>
              <strong aria-label="export result" role="link"> Export Result</strong>
          </button>
          <button aria-label="copy text" id='copyToClip' tabIndex="0" class='btn btn-primary btn-md copyToClip' onclick='copyToClipboard();' type='button'>
              <svg aria-label="copy icon" role="img" height='20' width='20' fill='none' viewBox='0 0 24 24' stroke-width='2' stroke='currentColor' aria-hidden='false'>
                  <path d='M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z' stroke-linecap='round' stroke-linejoin='round'>
                  </path>
              </svg>
              <strong aria-label="copy text" role="link"> Copy Text</strong>
          </button>
          `;

          var formattedPrompt = txtPrompt.value.replace(/\n/g, '<br>');
          // Call the function to add or update key-value pairs
          updatePromptAndOutput(output.data);
          // Call the function to append the updated key-value pairs to the div
          appendKeyValuesToResponseDiv();
          // Reset key-value pairs
          promptAndOutput = [];
        
        }
        // end of export to PDF logic
      } else {
        if(output.message) outputContainer.innerHTML = '<div class="alert alert-primary">'+ output.message +'</div>';
        else outputContainer.innerHTML = '<div class="alert alert-primary">'+ output.data +'</div>';
      }
    });

    //trigger when user clicks Generate Proper English button
    //TrustPilot logic change
    const TPLogicRun = window.TPLogicRun;
    console.log("TPLogicRun", typeof TPLogicRun);
    if (typeof TPLogicRun === 'function') {
      TPLogicRun();
    } else {
      console.error("TrustPilot TPLogicRun function failed to execute");
    }
  }

  function isParagraph(input) {
  const lines = input.split(/\r?\n/);
  const words = lines[0].split(/\s+/);

  return lines.length > 1 || words.length > 1;
  }


  $('#textfield-prompt').keyup(function(event) {
    // if (event.keyCode === 13) {
    //   generate();
    // }
  });



</script>
{% endblock %}