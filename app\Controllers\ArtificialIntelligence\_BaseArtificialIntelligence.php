<?php

namespace App\Controllers\ArtificialIntelligence;

use App\Controllers\BaseController;
use App\Models\UsageTrackingModel;

use BTCore\DataStorage as BTDataStorage;
use BTCore\DataStorage\RedisDB as BTRedisDB;


class _BaseArtificialIntelligence extends BaseController
{
    protected $isPrivate = false;
    protected $isProtected = false;

    protected $sessionId;
    protected $sessionName;
		protected $userDetails;
    protected $userIsPaid = false;

    //---------------------------------------------------------------------------------------------
    //  public
    //---------------------------------------------------------------------------------------------

    public function __construct()
    {
        header('X-Robots-Tag: noindex', true);
        // $this->authenticate();
    }

    // public function index()
    // {
    // }

    //---------------------------------------------------------------------------------------------
    //  protected
    //---------------------------------------------------------------------------------------------

    /**
     * authenticatePrivateAccess function
     *
     * @api $_SERVER['apiOpenAIURL']
     *  - take note that this function connects to $_SERVER['EXTERNAL_API_URL']
     * @return void
     */
    protected function authenticatePrivateAccess()
    {
        $aiwpUser = null;
        if (isset($_COOKIE['aiwp_logged_in'])) {
            $aiwpUser = $_COOKIE['aiwp_logged_in'];
        }
        if (isset($_COOKIE['aipro_wp_user'])) {
            $aiwpUser = $_COOKIE['aipro_wp_user'];
        }
        if (is_null($aiwpUser)) {
            return ACCESS_DENIED;
        }
        if ($aiwpUser == "") {
            return ACCESS_DENIED;
        }

        $curl = curl_init();
        $postFields = [
            'aiwp_logged_in' => $aiwpUser,
            'user_event_data' => []
        ];
        $postFields = (object) $postFields;

        curl_setopt_array($curl, array(
            CURLOPT_URL => $_SERVER['EXTERNAL_API_URL'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST", // Set request type to POST
            CURLOPT_POSTFIELDS => json_encode($postFields), // Set POST fields and values
        ));

        $response = curl_exec($curl); // Execute cURL request and store response in $response
        $arrResponse = json_decode($response, true);
        log_message('debug', $_SERVER['EXTERNAL_API_URL']);
        log_message('debug', json_encode($arrResponse));

        curl_close($curl); // Close cURL session

        //--> uncomment to print response to debug
        // echo "<pre>";
        // print_r($arrResponse);
        // echo "</pre>";
        // die;

        if (isset($arrResponse['success']) && $arrResponse['success'] == 1) {
            $this->userIsPaid = true;
						$this->userDetails = $arrResponse;
            return ACCESS_GRANTED;
        }

        // Passing here means email in cookie is not allowed to access APP pages
        $this->userIsPaid = false;
        return ACCESS_DENIED;
    }

    protected function filterRestrictions($reqSlug, $req = [])
    {
        if ($reqSlug == "") {
            return false;
        }

        $btEnum = btenum();
        if (!$btEnum['restrictionsProtectedAccess']) {
            return false;
        }

        if (!$btEnum['restrictionsProtectedAccess'][$reqSlug]) {
            return false;
        }

        if (!$btEnum['restrictionsProtectedAccess'][$reqSlug]['slug_access']) {
            return false;
        }

        if ($btEnum['restrictionsProtectedAccess'][$reqSlug]['slug_access']['restriction_code'] == isPROTECTED) {
            $restrictions = $btEnum['restrictionsProtectedAccess'][$reqSlug]['slug_access']['rules'];
            $resProtected = $this->authenticateProtectedAccess($reqSlug, $restrictions);
            if ($resProtected['success']) {
                return $this->response->setJSON($resProtected);
            }
        }

        return false;
    }

    protected function authenticateProtectedAccess($slug, $restrictions)
    {
        $res = [
            'success' => 0,
            'message' => 'Restricted.',
            'data' => '',
        ];

        $criteria = '';
        $value = '';
        $response = '';
        foreach ($restrictions as $field => $rules) {
            foreach ($rules as $key => $val) {
                if ($key == 'condition') {
                    $criteria = $val;
                } elseif ($key == 'value') {
                    $value = (float) $val;
                } elseif ($key == 'response') {
                    $response = $val;
                }
            }
        }

        $userEmail = btflag_cookie('aiwp_logged_in', 'unknown|');
        log_message('error', 'email -->' . json_encode($userEmail));
        $userEmail = urldecode($userEmail);
        $userEmail = explode("|", $userEmail);
        $userEmail = $userEmail[0];

        switch ($slug) {

            case 'start-chatbot':
                if ($criteria == "count_limit") {
                    $usageTrackingDataModel = new UsageTrackingModel();
                    $where = ['ip_address' => $this->getClientIP(), 'app' => $slug];
                    $countLimit = $usageTrackingDataModel->countLimitCriteria($where);
                    log_message('debug', 'start-chatbot: email -->' . json_encode($userEmail));
                    log_message('debug', 'start-chatbot: where -->' . json_encode($where));
                    log_message('debug', 'start-chatbot: limit -->' . json_encode($countLimit));
                    if ( $value <= $countLimit && $this->userIsPaid==false ) {
                        $res = $response;
                    }
                }
                break;
            case 'dream-photo':
            case 'text-to-image':
                if ($criteria == "count_limit") {
                    $usageTrackingDataModel = new UsageTrackingModel();
                    $where = ['ip_address' => $this->getClientIP(), 'app' => $slug];
                    $countLimit = $usageTrackingDataModel->countLimitCriteria($where);
                    log_message('debug', 'text-to-image: email -->' . json_encode($userEmail));
                    log_message('debug', 'text-to-image: where -->' . json_encode($where));
                    log_message('debug', 'TEXT-TO-IMAGE-COUNT-LIMIT: ' . json_encode($countLimit));
                    if ($value <= $countLimit && $this->userIsPaid==false) {
                        $res = $response;
                    }
                }
                break;
						case 'convert-to-proper-english':
							if ($criteria == "count_limit") {
									$usageTrackingDataModel = new UsageTrackingModel();
									$where = ['ip_address' => $this->getClientIP(), 'app' => $slug];
									$countLimit = $usageTrackingDataModel->countLimitCriteria($where);
									log_message('debug', 'convert-to-proper-english: email -->' . json_encode($userEmail));
									log_message('debug', 'convert-to-proper-english: where -->' . json_encode($where));
									log_message('debug', 'convert-to-proper-english-COUNT-LIMIT: ' . json_encode($countLimit));
									if ($value <= $countLimit && $this->userIsPaid==false) {
											$res = $response;
											$res['countLimit'] = $countLimit;
											$res['where'] = $where;
											$res['userEmail'] = $userEmail;
									}
							}
							break;
            default:
        }
        return $res;
    }

    protected function initializeSession()
    {
        // $_SESSION['AI_APP001_SID'] = btutilGenerateHashId(['1J3JfcGUH7BGtSe7']);
        // $_SESSION['AI_CHATBOT_SID'] = btutilGenerateHashId(['1J3JfcGUH7BGtSe7']);
    }

    protected function trackUsage($status, $data = [])
    {
        $userEmail = btflag_cookie('aiwp_logged_in', 'unknown|');
        $userEmail = explode("|", $userEmail);
        $sessionId = $data['requestData']['surfToken'];
        $userPrompt = json_encode($data['prompts']['user']);
        $otherPrompt = json_encode($data['prompts']['other']);

        $usageTrackingData = [
            'track_pid' => btutilGenerateUuid(),
            'session_id' => $sessionId,
            'user_email' => $userEmail[0],
            'ip_address' => $this->getClientIP(),
            'app' => trim($data['app']),
            'api_url' => current_url(),
            'user_prompt' => $userPrompt,
            'other_prompt' => $otherPrompt,
            'ai_response' => trim($data['ai_response']),
            'response_raw' => json_encode($data['promptResponseData']),
            'status' => $status,
        ];
        $usageTrackingData['response_usage'] = ($status == 'success') ? $data['response_usage'] : '';
        $usageTrackingData['request_raw'] = json_encode($usageTrackingData);

        $usageTrackingDataModel = new UsageTrackingModel();
        $process = $usageTrackingDataModel->insert($usageTrackingData, false);

        if ($process) {
            return $usageTrackingDataModel->getInsertID();
        }

        return false;
    }


    protected function catchException($e)
    {
        $errorCode = $e->getCode();
        $errorMsg = $e->getMessage();
        $status = btenumStatusCode();
        if (str_contains($e->getMessage(), '400 (Bad Request)')) {
            $errorCode = '400';
            $errorMsg = $status[$errorCode]->message;
            btsessionSet($this->sessionName, [
                "user_prompts" => [],
            ]);
        } elseif (str_contains($e->getMessage(), 'Response body parse failed')) {
            $errorCode = '500';
            $errorMsg = $status[$errorCode]->message;
            btsessionSet($this->sessionName, [
                "user_prompts" => [],
            ]);
        } elseif (str_contains($e->getMessage(), 'Internal Server Error')) {
            $errorCode = '500';
            $errorMsg = $status[$errorCode]->message;
            btsessionSet($this->sessionName, [
                "user_prompts" => [],
            ]);
        }
        $res = [
            'success' => 0,
            'message' => "[`$errorCode`]" . $errorMsg,
            'data' => $errorMsg,
        ];
        $usageTrackingData['ai_response'] = $e;
        $usageTrackingData['promptResponseData'] = $res;
        $this->trackUsage('failed', $usageTrackingData);

        \Sentry\captureException($e);
        log_message('error', $errorCode);
        log_message('error', $errorMsg);
        return $this->response->setStatusCode($errorCode, $errorMsg);
    }

    protected function validateAsk($prompt)
    {
        if(getenv("ENABLE_MODERATION") !== '1') return false;
        $url = 'https://api.openai.com/v1/moderations';
        $ch_data = [
            'input' => $prompt
        ];
        //
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type:application/json', 'Authorization: Bearer '.getenv('OPENAI_API_KEY')));
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($ch_data));
        $response = json_decode(curl_exec($ch));
        curl_close($ch);

        if($response->results[0]->flagged) {
            $res = [
                'success' => 0,
                'message' => "",
                'data' => "I'm sorry, but I can't assist with that. We want everyone to use our tool safely and responsibly.\nIf you have any other questions or need advice on a different topic, feel free to ask."
            ];

            return $res;
        }

        return false;
    }

		protected function getRegisterUrl()
		{
			$register_url = '/register';
			$wp_flow = btflag_cookie('flow', '');

			if(btflag_cookie('regRedirectWP', '0') === '1'){
					switch ($wp_flow) {
							case '02':
									$register_url = '/register-b';
							break;
							case '03':
									$register_url = '/register-x';
							break;
							case '04':
									$register_url = '/select-account-type-d'; //splash
							break;
							case '05':
									$register_url = '/aurora-register';
							break;
							default:
									$register_url = $register_url;
					}
			}

			return $register_url;
		}

    //---------------------------------------------------------------------------------------------
    //  private
    //---------------------------------------------------------------------------------------------

    private function getClientIP()
    {
      $ipaddress = '';
      if (isset($_SERVER['HTTP_CLIENT_IP']))
        $ipaddress = $_SERVER['HTTP_CLIENT_IP'];
      else if (isset($_SERVER['HTTP_X_FORWARDED_FOR']))
        $ipaddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
      else if (isset($_SERVER['HTTP_X_FORWARDED']))
        $ipaddress = $_SERVER['HTTP_X_FORWARDED'];
      else if (isset($_SERVER['HTTP_X_CLUSTER_CLIENT_IP']))
        $ipaddress = $_SERVER['HTTP_X_CLUSTER_CLIENT_IP'];
      else if (isset($_SERVER['HTTP_FORWARDED_FOR']))
        $ipaddress = $_SERVER['HTTP_FORWARDED_FOR'];
      else if (isset($_SERVER['HTTP_FORWARDED']))
        $ipaddress = $_SERVER['HTTP_FORWARDED'];
      else if (isset($_SERVER['REMOTE_ADDR']))
        $ipaddress = $_SERVER['REMOTE_ADDR'];
      else
        $ipaddress = '*********';

      if ($ipaddress != '') {
          $arr_ip = explode(',', $ipaddress);
          $ipaddress = isset($arr_ip[0]) ? $arr_ip[0] : '';
      }

      return $ipaddress;
    }

}
