import { cn } from "../utils/cn";

export default function Tooltip({
  tooltip,
  className,
}: {
  tooltip: string;
  className?: string;
}) {
  if (!tooltip) {
    return null;
  }

  const width = tooltip.length * 7 + 10;
  const right = -(width / 2) + 10;
  return (
    <>
      <div
        className={cn(
          className,
          "absolute bottom-[40px] w-max max-w-[200px] transform rounded-md bg-black p-2 text-white opacity-100 shadow-lg dark:bg-white dark:text-black",
        )}
        style={{ width: width + "px", right }}
      >
        <p className="text-center text-xs font-semibold">{tooltip}</p>
        <svg
          className="absolute -bottom-1 left-1/2 -translate-x-1/2 transform"
          width="8"
          height="4"
          viewBox="0 0 8 4"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M4 4L0 0H8L4 4Z"
            fill="currentColor"
            className="fill-black dark:fill-white"
          ></path>
        </svg>
      </div>
    </>
  );
}
