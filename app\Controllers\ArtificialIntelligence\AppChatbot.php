<?php

namespace App\Controllers\ArtificialIntelligence;

use App\Controllers\ArtificialIntelligence\_BaseArtificialIntelligence as AIBase;

class AppChatbot extends AIBase
{
    protected $isProtected = ISPROTECTED_APPCHATBOT;

    private $_id = 'APPCHATBOT'; //not used
    private $theme = 'avalanche';
    private $themePageVersion = '02'; //not really used
    private $themeSlug = null;

    public function __construct()
    {
        parent::__construct();
    }

    public function index($slug = 'default')
    {
        $this->themeSlug = $slug;
        $this->initSession();

        // -------------------------------------
        // FILTER: authenticatePrivateAccess
        // -------------------------------------
        switch ($this->themeSlug) {
            case "start-chatbot":
            case "chatbot":
                setcookie("app", "basic", time() + (86400 * 30), "/", ".ai-pro.org");
                break;
            // commented out; for this will be handledin authV2 JS
            // case "chatbot":
                // if (!$this->authenticatePrivateAccess()) {
                //     return redirect()->to(AUTH_FAILED_REDIRECTION_URL);
                // }
                // break;
        }

        $this->theme = btflag('theme', $this->theme);


        $this->theme_data();
        $this->theme_pageVersion();

        switch ($this->theme) {
            case "avalanche":
            default:
                $this->theme_avalanche();
        }
    }


    //---------------------------------------------------------------------------------------------
    //  protected
    //---------------------------------------------------------------------------------------------

    protected function initSession()
    {
        $date_now = date('Y-m-d H:i:s', time());
        $this->sessionId = btutilGenerateHashId([$date_now, '1J3JfcGUH7BGtSe7']);
        $sessionName = $this->themeSlug . '_' . $this->sessionId;
        if (!$this->session->has($sessionName)) {
            $this->session->set($sessionName, []);
        }


        // $_SESSION['AI_CHATBOT_USERPROMPT'] = "";
        $this->session->push($sessionName, [
            'user_prompts' => [],
            'ai_responses' => [],
            'conversation' => [],
            'surfToken' => $this->sessionId,
        ]);
    }


    //---------------------------------------------------------------------------------------------
    //  private
    //---------------------------------------------------------------------------------------------

    private function theme_avalanche()
    {
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData
        ];

        $twigFile = $this->themeSlug;

        // do not edit this
        $this->bttwig->view("/btcomponents/chatbot_client_v2/page_chatbotClientV2_{$twigFile}.twig", $viewData);
    }

    private function theme_data()
    {

        $api_server_url = base_url() . 'api/openai/v1/chat-completion';
        $mixpanel = false;

        $OPENSOURCE_ENDPOINT = getenv('OPENSOURCE_ENDPOINT');
        if ($OPENSOURCE_ENDPOINT) {
            $api_server_url =   base_url() . 'api/opensource/v1/chat-completion';
        }
        if($this->themeSlug == 'start-chatbot') {
            $mixpanel = true;
        }

        $this->themePageData = [
            'api_server_url' => $api_server_url,
            'start_server_url' => getenv('START_URL') ? getenv('START_URL') : 'https://start.ai-pro.org',
            'body_class' => 'app',
            'page_title' => 'ChatBOT',
            'has_back_button' => $this->themeSlug == 'chatbot' ? 1 : 0,
            'app_dashboard_url' => getenv('DASHBOARD_REDIRECTION_URL'),
            'btutil_auth_url' => $this->themeSlug == 'chatbot' ? getenv('BTUTIL_ASSET_URL') : getenv('API_URL') . '/ext-app/js/btutil-core.min.js?ver=',
            'meta_data' => [
                'description' => '',
                // 'robots' => 'noindex, follow', //uncomment and change as needed
            ],
            'slug' => $this->themeSlug,
            'chatbotURL' => getenv('chatbotURL'),
            'surfToken' => ['name' => 'surfToken', 'hash' => $this->sessionId],
            'include_mixpanel' => $mixpanel ? [
                'debug' => CONFIG_MIXPANEL_DEBUG,
                'mixpanel_name' => 'app_chatbot',
                'keyword' => btflag('keyword', ''),
                'emailid' => btflag('emailid', ''),
                'adid' => btflag('adid', ''),
                'ppg' => btflag('ppg', ''),
                'pmt' => btflag('pmt', ''),
                'email' => btflag_cookie('user_email', ''),
                'user_plan' => btflag_cookie('user_plan', ''),
                'howdoiplantouse' => btflag_cookie('howdoiplantouse', ''),
                'remakemedloption' => btflag_cookie('remakemedloption', '')
            ] : false,
            'init_mixpanel' => true,
            'include_gtag_AW' => true,
            'include_gtag_GA4' => true
        ];
    }

    private function theme_pageVersion()
    {
        // not yet really used
        $this->themePageVersion = btflag_get('v', $this->themePageVersion);
        return $this->themePageVersion;
    }

}
