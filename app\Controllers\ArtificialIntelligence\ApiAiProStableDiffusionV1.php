<?php

namespace App\Controllers\ArtificialIntelligence;

use App\Controllers\ArtificialIntelligence\_BaseArtificialIntelligence as AIBase;

use <PERSON><PERSON><PERSON>\ArtificialIntelligence as BTAI;
use <PERSON><PERSON>ore\ArtificialIntelligence\AiProStableDiffusionV1 as BTAiProStableDiffusionV1;

use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;


/**
 *  AIPro Stable Diffusion API --> https://sd.ai-pro.org/sdapi/v1
 */
class ApiAiProStableDiffusionV1 extends AIBase
{
    //---------------------------------------------------------------------------------------------
    //  variables
    //---------------------------------------------------------------------------------------------

    protected $app;
    protected $appClass;

    //---------------------------------------------------------------------------------------------
    //  public
    //---------------------------------------------------------------------------------------------

    public function __construct()
    {
        parent::__construct();
        header('Access-Control-Allow-Origin: *');

        $this->app = new BTAI();
        $this->appClass = new BTAiProStableDiffusionV1();
        $this->app->_init($this->appClass);
        $this->app->base64Code = "1J3JfcGUH7BGtSe7";
    }

    public function v1($module = '')
    {
        $res = $this->appResponse;
        $resDeniedAccess = [
            'success' => 0,
            'message' => 'Access denied.',
        ];
        $req = $this->request->getPost();

        switch ($module) {
            case 'create-art':
                $res = $this->textToImage($req);
            break;
            case 'dream-photo':
                $res = $this->textToImageV2($req);
            break;
            default:
        }

        return $this->response->setJSON($res);
    }

    public function v2($module = '')
    {
        $res = $this->appResponse;
        $resDeniedAccess = [
            'success' => 0,
            'message' => 'Access denied.',
        ];
        $req = $this->request->getPost();

        if(!$this->authenticatePrivateAccess()) {
					switch ($module) {
						case 'dream-photo-create':
							$reqSlug = 'dream-photo';
							$resRestrictions = $this->filterRestrictions($reqSlug, $req);
							if ($resRestrictions) {
									$userEmail = btflag_cookie('aiwp_logged_in', 'unknown|');
									$tool_count = $this->getToolCount();
									$start_url = getenv('START_URL') ? getenv('START_URL') : START_URL;
									$ctaModal_logic = btflag_cookie('regRedirectWP', '0') === '1' ? WP_URL : $start_url;
									$wp_flow = btflag_cookie('flow', '');
									$register_url = '/register';

									if(btflag_cookie('regRedirectWP', '0') === '1'){
											switch ($wp_flow) {
													case '02':
															$register_url = '/register-b';
													break;
													case '03':
															$register_url = '/register-x';
													break;
													case '04':
															$register_url = '/select-account-type-d'; //splash
													break;
													case '05':
															$register_url = '/aurora-register';
													break;
													default:
															$register_url = $register_url;
											}
									}

									$title = 'Switch to PRO to Continue Using DreamPhoto';
									$redirectURL = $start_url . '/pricing';

									if($userEmail === 'unknown|') {
											$redirectURL = $ctaModal_logic . $register_url;
									}

									if(btflag(FLOW, null) === 'basilisk-02') {
											$redirectURL = $start_url . '/splash';
									}

									return $this->response->setJSON([
											'success' => 2,
											'message' => 'Restricted.',
											'data' => $title,
											'modal' => [
													'title' => $title,
													'subTitle' => 'Enjoying the trial version of DreamPhoto so far?',
													'desc' => 'Switch now to PRO and get access to <span>'.$tool_count.'</span> different creativity and productivity AI tools.',
													'ctaLabel' => 'CONTINUE',
													'redirectUrl' => $redirectURL
											]
									]);

							}
					}
        }
        switch ($module) {
            case 'dream-photo-create':
                $res = $this->textToImageV2($req);
                $res['user'] = $this->userDetails;
            break;
            case 'dream-photo-upscale':
                $res = $this->upscaleImage($req);
            break;
            default:
        }

        return $this->response->setJSON($res);
    }


    //---------------------------------------------------------------------------------------------
    //  private
    //---------------------------------------------------------------------------------------------

    private function textToImage($req = [])
    {
        log_message("debug", "textToImage");

        $res = $this->appResponse;
        if (!isset($req['prompt'])) {
            return $res;
        }

        $userPrompt = json_decode($req['prompt'], true);
        $message = $userPrompt['positive'];
        $hasNegativePrompt = !empty($userPrompt['negative']);
        $predefined_negative = '(deformed, distorted, disfigured:1.3), poorly drawn, bad anatomy, wrong anatomy, extra limb, missing limb, floating limbs, (mutated hands and fingers:1.4), disconnected limbs, mutation, mutated, ugly, disgusting, blurry, amputation, BadDream, (UnrealisticDream:1.3)';
        $negativePrompt = $hasNegativePrompt ? $userPrompt['negative'] . ', ' . $predefined_negative : $predefined_negative;

        $slug = $req['slug'];

        $client = new Client();
        $params['headers'] = ['Content-Type' => 'application/json'];

        $params['json'] = [
            'prompt' => $message,
            'negative_prompt' => $negativePrompt,
            'sampler_name' => 'DPM++ 2M SDE',
            'restore_faces' => true,
            'steps' => 30,
            'batch_size' => 2,
            "denoising_strength" => 0.45,
            'seed' => -1,
            'n_iter' => 1,
            'tiling' => false, //this is the default
            'width' => 512,
            'height' => 512,
        ];

        foreach($params['json'] as $key => $value) {
            $userPrompt[$key] = $value;
        }

        $res_prompt = null;
        $usageTrackingData = [
            'requestData' => $req,
            'prompts' => [
                'user' => $req['prompt'],
                'other' => $predefined_negative,
            ],
            'app' => $slug,
            'response_usage' => '',
        ];

        try {
            $response = $this->app->imageGenerationV2($message, $userPrompt);
            $res_data = json_decode($response,true);
            unset($res_data['parameters']); //removing this in API response; we do not want to show this
            unset($res_data['info']); //removing this in API response; we do not want to show this

            $res = [
                'success' => $res_data['success'],
                'message' => $res_data['message'], // TODO: change to OK
                'data' => $res_data,
                'usageCounter' => json_encode([
                    'total_tokens' => $params['json']['batch_size'],
                    'size' => [
                        'width' => $params['json']['width'],
                        'height' => $params['json']['height'],
                    ],
                ]),
            ];
            $usageTrackingData['ai_response'] = json_encode($res['data']);
            $usageTrackingData['response_usage'] = $res['usageCounter'];
        } catch (\Exception$e) {
            return $this->catchException($e);
        }


        $usageTrackingData['promptResponseData'] = $response;

        //empty
        $usageTrackingData['promptResponseData'] = '';
        $usageTrackingData['ai_response'] = '';
        $usageTrackingData['promptResponseData'] = '';

        // btutilDebug($usageTrackingData);

        $this->trackUsage('success', $usageTrackingData);
        // unset($res['usageCounter']);
        return $res;
    }

    private function textToImageV2($req = [])
    {
        log_message("debug", "textToImage");

        $res = $this->appResponse;
        if (!isset($req['payload'])) {
            return $res;
        }

        $payload = json_decode($req['payload'], true);
        $positive_prompt = $payload['positive'];
        $payload['header'] = ['Content-Type: application/json'];
        $slug = $req['slug'];
        $usageTrackingData = [
            'requestData' => $req,
            'prompts' => [
                'user' => $req['payload'],
                'other' => '',
            ],
            'app' => $slug,
            'response_usage' => '',
        ];

        try {
            $response = $this->app->imageGenerationV2($positive_prompt, $payload);
            $res_data = json_decode($response,true);
            unset($res_data['parameters']); //removing this in API response; we do not want to show this
            unset($res_data['info']); //removing this in API response; we do not want to show this

            $res = [
                'success' => $res_data['success'],
                'message' => $res_data['message'], // TODO: change to OK
                'data' => $res_data,
                'usageCounter' => json_encode([
                    'total_tokens' => '2',
                    'size' => [
                        'width' => $payload['width'],
                        'height' => $payload['height'],
                    ],
                ]),
            ];
            $usageTrackingData['ai_response'] = json_encode($res['data']);
            $usageTrackingData['response_usage'] = $res['usageCounter'];
        } catch (\Exception$e) {
            return $this->catchException($e);
        }


        $usageTrackingData['promptResponseData'] = $response;

        //empty
        $usageTrackingData['promptResponseData'] = '';
        $usageTrackingData['ai_response'] = '';
        $usageTrackingData['promptResponseData'] = '';

        // btutilDebug($usageTrackingData);

        $this->trackUsage('success', $usageTrackingData);
        // unset($res['usageCounter']);
        return $res;
    }

    private function upscaleImage($req = [])
    {
        log_message("debug", "upscaleImage");

        $res = $this->appResponse;
        if (!isset($req['payload'])) {
            return $res;
        }

        $payload = json_decode($req['payload'],true);

        try {
            $response = $this->app->upscaleImage($payload);
            $res_data = json_decode($response,true);
            if($res_data['images']){
                $res = [
                    'success' => '1',
                    'message' => 'OK',
                    'data' => $res_data
                ];
            }
        } catch (\Exception$e) {
            echo 'Caught exception: ',  $e->getMessage(), "\n";
        }
        // unset($res['usageCounter']);
        return $res;
    }

}
