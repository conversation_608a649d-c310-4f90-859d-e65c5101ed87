import BackIcon from "./icons/BackIcon";
import SparkleIcon from "./icons/SparkleIcon";
import Diamond from "./icons/Diamond";
import { cn } from "../utils/cn";
import { useEffect, useState } from "react";
import { setCookie, getCookie } from "../utils/cookie";

export default function Header({
  className = "",
  setShowPricing,
  appName = "",
  app = "",
}: {
  className?: string;
  setShowPricing: (value: boolean) => void;
  appName?: string;
  app?: string;
}) {
  const [isProPlan, setIsProPlan] = useState<boolean>(true);
  const [hasEmail, setEmail] = useState<string | undefined>("");

  useEffect(() => {
    setEmail(getCookie("user_email"));
  }, []);

  useEffect(() => {
    const plan = window.subscription_type?.toString().toLowerCase();
    const isPro = !!plan && plan !== "basic";
    setIsProPlan(isPro);
  }, []);

  const startUrl = import.meta.env.VITE_START_URL || "https://start.ai-pro.org";
  const handleBackClick = () => {
    window.location.href = `${startUrl}/my-account`;
  };

  const onClickGoPro = () => {
    setCookie("app", app, { domain: ".ai-pro.org" });
    setCookie("appName", appName, { domain: ".ai-pro.org" });

    const plan = window.subscription_type?.toString().toLowerCase();

    if (plan === "basic") {
      setCookie("kt8typtb", "arcana", { domain: ".ai-pro.org" });
      setCookie("ppg", "59", { domain: ".ai-pro.org" });
      setTimeout(() => {
        window.location.href = `${startUrl}/upgrade?app=${app}&ppg=59&appName=${encodeURI(appName)}&utpm=`;
      }, 300);
      return;
    }
    if (hasEmail) {
      setCookie("kt8typtb", "arcana", { domain: ".ai-pro.org" });
      setCookie("ppg", "41", { domain: ".ai-pro.org" });
      setTimeout(() => {
        window.location.href = `${startUrl}/pricing?app=${app}&ppg=41&appName=${encodeURI(appName)}`;
      }, 300);
      return;
    }
    setShowPricing(true);
  };

  return (
    <>
      <header
        className={cn(
          "flex h-[68px] min-h-[68px] items-center justify-between px-[20px]",
          className,
        )}
      >
        <button
          onClick={handleBackClick}
          className="flex cursor-pointer items-center px-[8px] py-[11px] text-[14px] text-black dark:text-white"
        >
          <BackIcon className="text-black-100 mr-[10px] dark:text-white" />
          Back to Dashboard
        </button>

        {!isProPlan && (
          <>
            <button
              onClick={onClickGoPro}
              className="bg-gradient-blue hidden cursor-pointer items-center gap-[4px] rounded-3xl py-[11px] pr-[28.5px] pl-[22.5px] text-[14px] font-bold text-white md:flex dark:text-white"
            >
              <SparkleIcon size="18" className="text-white" />
              Go Pro
            </button>

            <button
              onClick={onClickGoPro}
              className="bg-gradient-blue flex cursor-pointer items-center rounded-3xl p-[10px] font-bold text-white md:hidden"
            >
              <Diamond size="16" />
            </button>
          </>
        )}
      </header>
    </>
  );
}
