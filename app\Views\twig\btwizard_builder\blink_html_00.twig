{% extends 'base_00.twig' %}
{% block styles %}
  {{ parent() }}
  <link rel='stylesheet' href='https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css?ver={{ GLOBAL_DATA.BUILD }}'>
    <link rel='stylesheet' href='https://cdn.form.io/formiojs/formio.full.min.css?ver={{ GLOBAL_DATA.BUILD }}'>
    {% endblock %}
    {% block pre_scripts %}
      {{ parent() }}
      <script src='https://cdn.form.io/formiojs/formio.full.min.js?ver={{ GLOBAL_DATA.BUILD }}'></script>
      <script type='text/javascript'>
		window.onload = function () {
var force = 1;
var builder_template = localStorage.getItem('btwzrdfrmtmplt');
if (builder_template == null || force == 1) {
builder_template = JSON.stringify({{ VIEW_DATA.btwizard_formio_forms | raw }});
localStorage.setItem('btwzrdfrmtmplt', builder_template);
}

Formio.builder(document.getElementById('builder'), JSON.parse(builder_template), {
builder: {
basic: false,
advanced: false,
data: false,
layout: false,
premium: false,
customBasic: {
title: 'Components',
default: true,
weight: 0,
components: {
section: {
title: 'Panel: Slide Header',
key: 'section',
icon: 'list-alt',
weight: 0,
schema: {
title: 'Slide X',
tableView: false,
type: 'panel',
key: 'section',
input: false,
property: 'section'
}
},
text: {
title: 'Text',
key: 'text',
icon: 'terminal',
weight: 1,
schema: {
label: 'Text Field',
description: '[FIELD X]',
tableView: false,
type: 'textfield',
key: 'text',
input: true,
validate: {
maxLength: 255
}
}
},
number: {
title: 'Number',
key: 'number',
icon: 'hashtag',
weight: 2,
schema: {
label: 'Number',
description: '[FIELD X]',
mask: false,
tableView: false,
delimiter: false,
requireDecimal: false,
inputFormat: 'plain',
truncateMultipleSpaces: false,
key: 'number',
type: 'number',
input: true
}
},
dropdown: {
title: 'Dropdown',
key: 'dropdown',
icon: 'list',
weight: 3,
schema: {
label: 'Dropdown',
description: '[FIELD X]',
tableView: false,
type: 'select',
key: 'dropdown',
input: true
}
},
date: {
title: 'Date',
key: 'date',
icon: 'calendar',
weight: 4,
schema: {
label: 'Date',
description: '[FIELD X]',
format: 'MMMM dd, yyyy',
tableView: false,
type: 'datetime',
key: 'date',
input: true,
enableTime: false,
property: 'date'
}
},
textarea: {
title: 'Textarea',
key: 'textarea',
icon: 'font',
weight: 5,
schema: {
label: 'Text Area',
description: '[FIELD X]',
tableView: false,
type: 'textarea',
key: 'textarea',
input: true
}
},
radio: {
title: 'Radio',
key: 'radio',
icon: 'dot-circle-o',
weight: 6,
schema: {
label: 'Radio',
optionsLabelPosition: 'right',
description: '[FIELD X]',
inline: false,
tableView: false,
values: [],
type: 'radio',
key: 'radio',
input: true
}
},
checkbox: {
title: 'Checkbox',
key: 'checkbox',
icon: 'check-square',
weight: 7,
schema: {
label: 'Checkbox',
description: '[FIELD X]',
inline: false,
tableView: false,
values: [],
type: 'checkbox',
key: 'checkbox',
input: true,
defaultValue: false
}
},
signature: {
title: 'Signature',
key: 'signature',
icon: 'pencil',
weight: 8,
schema: {
label: 'Text Field',
description: '[FIELD X]',
tableView: false,
type: 'signature',
key: 'signature',
input: false
}
},
ltr2pdfContent: {
title: 'Content',
key: 'ltr2pdfContent',
icon: 'font',
weight: 20,
schema: {
label: 'Content',
customClass: 'form-body',
html: "<p><span style=\"font-family:'Times New Roman', Times, serif;\">Paragraph X</span></p>",
tableView: false,
type: 'content',
key: 'ltr2pdfContent',
input: false
}
}
}
},
custom: {
title: 'Custom Fields',
default: true,
components: {
us_state: {
title: 'US State',
key: 'us_state',
icon: 'th-list',
weight: 2,
schema: {
label: 'US State',
description: '[FIELD X]',
widget: 'html5',
placeholder: 'This is a US State placeholder only',
tableView: false,
dataSrc: 'custom',
key: 'us_state',
type: 'select',
input: true
}
},
heading: {
title: 'Heading',
key: 'heading',
icon: 'code',
weight: 4,
schema: {
label: 'Heading',
attrs: [],
content: 'Please review your document before submitting.',
refreshOnChange: false,
key: 'heading',
type: 'htmlelement',
input: false,
tableView: false
}
}
}
}
}
}).then(function (form) {
form.on("change", function (e) {
console.log(e);
console.log(form);
localStorage.setItem('btwzrdfrmtmplt', JSON.stringify(e));
});
});
};
	</script>
    {% endblock %}
    {% block body_header %}
      {{ parent() }}
    {% endblock %}
    {% block body_modal %}
      {{ parent() }}
    {% endblock %}
    {% block body_content %}
      <div style="padding: 50px">
        <div id='builder'></div>
      </div>
    {% endblock %}
    {% block body_footer %}
      {{ parent() }}
    {% endblock %}
    {% block post_scripts %}
      {{ parent() }}
    {% endblock %}
    