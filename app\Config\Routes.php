<?php

namespace Config;

// Create a new instance of our RouteCollection class.
$routes = Services::routes();

/*
 * --------------------------------------------------------------------
 * Router Setup
 * --------------------------------------------------------------------
 */
$routes->setDefaultNamespace('App\Controllers');
$routes->setDefaultController('Home');
$routes->setDefaultMethod('index');
$routes->setTranslateURIDashes(false);
$routes->set404Override();
$routes->setAutoRoute(false);

/*
 * --------------------------------------------------------------------
 * Route Definitions
 * --------------------------------------------------------------------
 */

// We get a performance increase by specifying the default
// route since we don't have to scan directories.
$routes->get('/', 'ArtificialIntelligence\App::index');


$routes->get('/chatbot', 'ArtificialIntelligence\AppChatbot::index/chatbot');
$routes->get('/start-chatbot', 'ArtificialIntelligence\AppChatbot::index/start-chatbot');
$routes->get('/convert-to-proper-english', 'ArtificialIntelligence\App::index/convert-to-proper-english');
$routes->get('/create-art', 'ArtificialIntelligence\App::index/create-art');
$routes->get('/dream-photo', 'ArtificialIntelligence\App::index/dream-photo');
$routes->get('/text-to-image', 'ArtificialIntelligence\App::index/text-to-image');


/*
 * --------------------------------------------------------------------
 * Additional Routing
 * --------------------------------------------------------------------
 *
 * There will often be times that you need additional routing and you
 * need it to be able to override any defaults in this file. Environment
 * based routes is one such time. require() additional route files here
 * to make that happen.
 *
 * You will have access to the $routes object within that file without
 * needing to reload it.
 */
if (is_file(APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php')) {
    require APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php';
}

if (is_file(APPPATH . 'Config/' . getenv('SITE_ENVIRONMENT') . '/Routes.php')) {
    require APPPATH . 'Config/' . getenv('SITE_ENVIRONMENT') . '/Routes.php';
}
