<?php


/*
 * --------------------------------------------------------------------
 * Dev Route Definitions
 * --------------------------------------------------------------------
 */

require APPPATH . 'Config/Admin/Routes.php';
require APPPATH . 'Config/Api/Routes.php';

// $routes->group("ai", ["namespace" => "App\Controllers\ArtificialIntelligence"], function($routes){
//     $routes->get("api", "Api::index");
// });


//improve naming
$routes->get('/ai/builder', 'ArtificialIntelligence\Builder::index'); //Admin Builder
$routes->get('/ui/builder', 'ArtificialIntelligence\Builder::index'); //User Builder
$routes->get('/demo/formio/builder', 'Formio\Builder::index'); //User Builder


// $routes->get('/ai/', 'ArtificialIntelligence\Home::index');


$routes->get('generate/(:any)', 'ArtificialIntelligence\App::index/$1');



$routes->get('/test', 'ArtificialIntelligence\App::index/test');


$routes->get('/formio/builder', 'Formio\Builder::index');


$routes->get('/debug', 'Dev::debugClientSession');