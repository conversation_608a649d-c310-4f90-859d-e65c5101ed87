<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateTblUsageTracking extends Migration
{
    private $table = 'tbl_usageTracking';

    public function up()
    {
        $this->forge->addField([
            'track_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => false,
                'auto_increment' => true,
            ],
            'track_pid' => [
                'type' => 'VARCHAR',
                'constraint' => 36,
                'null' => false,
                'comment' => 'UUID; Public ID; reference ID for external use such as in URL, storage folder/file name',
            ],
            'user_email' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
            ],
            'ip_address' => [
                'type' => 'VARCHAR',
                'constraint' => 15,
                'null' => false,
            ],
            'session_id' => [
                'type' => 'VARCHAR',
                'constraint' => 36,
                'null' => false,
                'comment' => 'Session ID',
            ],
            'app' => [
                'type' => 'VARCHAR',
                'constraint' => 250,
                'null' => true,
            ],
            'api_url' => [
                'type' => 'VARCHAR',
                'constraint' => 250,
                'null' => true,
            ],
            'user_prompt' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Contains user input only',
            ],
            'other_prompt' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Contains default, suffix, and other',
            ],
            'ai_response' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Contains the AI Engine response.',
            ],
            'request_raw' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'response_usage' => [
                'type' => 'TINYTEXT',
                'null' => true,
            ],
            'response_raw' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'status' => [
				'type' => "SET('success','failed')",
				'comment' => '',
			],
            'created_at' => [
                'type' => 'DATETIME',
            ],
            'updated_at' => [
                'type' => 'DATETIME',
            ],
            'deleted_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ]
        ]);
        $this->forge->addKey('track_id', true);
        $this->forge->createTable($this->table);
    }

    public function down()
    {
        // TODO: always disable this function on deployment
        // $this->forge->dropTable($this->table);
    }
}