{% extends '_base_template_C.twig' %}

{% block head_styles %}
{{ parent() }}
<link rel="stylesheet" href="/themes/avalanche/C01/main.min.css?ver={{ BASE.build }}" />
<link rel='stylesheet' href='https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css?ver={{ BASE.build }}' />
<link rel='stylesheet' href='https://cdn.form.io/formiojs/formio.full.min.css?ver={{ BASE.build }}' />
<link rel='stylesheet' href='/themes/avalanche/css/builder/main.css?ver={{ BASE.build }}'>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/izitoast/1.4.0/css/iziToast.css" integrity="sha512-DIW4FkYTOxjCqRt7oS9BFO+nVOwDL4bzukDyDtMO7crjUZhwpyrWBFroq+IqRe6VnJkTpRAS6nhDvf0w+wHmxg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<style>
  .component-edit-container {
    overflow: visible !important;
  }
</style>
{% endblock %}

{% block head_scripts %}
{{ parent() }}
<script src='https://cdn.form.io/formiojs/formio.full.min.js?ver={{ BASE.build }}'></script>
<script type="text/javascript" src="{{ BASE.base_url }}assets/js/customFormio.js?ver={{ BASE.build }}"></script>
<script type='text/javascript'>
  var appId = '{{ PAGE.btform.textAppId }}';
  var btformData = JSON.stringify({{ PAGE.btform.formio_data | raw }});
  var btformForm = JSON.stringify({{ PAGE.btform.formio_form | raw }});
  var btformOptions = JSON.stringify({{ PAGE.btform.formio_options | raw }});
  var form_data;

  window.onload = function () {

    Formio.createForm(document.getElementById('btformIo'),
      JSON.parse(btformData) ,
      {}
      ).then(function (form) {
        form.nosubmit = true;

        form.ready.then(() => {


          Formio.builder(document.getElementById('builder'),
            JSON.parse(btformForm) ,
            JSON.parse(btformOptions) )
          .then(function (form) {
            form.on("change", function (e) {
              // console.log(JSON.stringify(e));
              {# localStorage.setItem("btformData", JSON.stringify(e)); #}
            });
          });
        });

        form.on('submit', function(resData) {
          let submitUrl = 'http://localhost:9000/artificialintelligence/crudl/CreateAppForm';
          let formData = new FormData();
          formData.append('res', JSON.stringify(resData));

          return Formio.fetch(submitUrl, {
            body: formData,
            method: 'POST',
            mode: 'cors',
          })
          .then(function(response) {
            console.log(resData);
            form.emit('submitDone', resData)
            response.json()
            iziToast.success({
                title: 'OK',
                message: 'Successfully inserted record!',
            });
          })
        });
      }
    );
  };
</script>
{% endblock %}


{% block body %}
<div class="layout has-sidebar fixed-sidebar fixed-header">
  {% include 'avalanche/_C01_common/body_sidebar.twig' %}
  <div id="overlay" class="overlay"></div>
  <div class="layout">
    {% include 'avalanche/_C01_common/body_header.twig' %}
    <main class="content">
      {% include 'avalanche/aiBuilder/page_aiBuilder_C01_body_content.twig' %}
    </main>
    <div class="overlay"></div>
  </div>
</div>
{% endblock %}


{% block body_hidden_content %}
  {{ parent() }}
{% endblock %}


{% block body_scripts %}
{{ parent() }}
<script src="/themes/avalanche/C01/main.min.js?ver={{ BASE.build }}"></script>
<script src="/assets/common/js/toastr.min.js?ver={{ BASE.build }}"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/izitoast/1.4.0/js/iziToast.min.js" integrity="sha512-Zq9o+E00xhhR/7vJ49mxFNJ0KQw1E1TMWkPTxrWcnpfEFDEXgUiwJHIKit93EW/XxE31HSI5GEOW06G6BF1AtA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
{% endblock %}
