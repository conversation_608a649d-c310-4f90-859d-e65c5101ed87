<?php

use Config\Services as CI4Services;

/**------------------------------------------------------------------------------
 * BTSession
 -------------------------------------------------------------------------------*/

/**
 * 	Initialize
 */
function btsessionInit() {
	return CI4Services::session();
}

/**
 * 	Set per Module
 */
function btsessionSet($_module, $_data) {
	$session = btsessionInit();

	// 1:many; an array
	if (btsessionHas($_module)) {
		$data = array_merge($_SESSION[$_module], $_data);
		$session->push($_module, $data);
		return;
	}

	// 1:1; key-value
	$session->set($_module, $_data);
}


/**
 * 	Get per Module
 */
function btsessionGet($_module) {
	if (is_cli()) {
		return;
	}

	$session = btsessionInit();

	if (!btsessionHas($_module)) {
		return 0;
	}

	return (object) $session->get($_module);
}

/**
 * 	Remove per Module
 */
function btsessionClean($_module) {
	$session = btsessionInit();
	$session->remove($_module);
}

/**
 * 	Destroy/Remove All
 */
function btsessionDestroy() {
	$session = btsessionInit();
	$session->destroy();
}

/**
 * 	Has Module
 */
function btsessionHas($_module) {
	$session = btsessionInit();
	return $session->has($_module);
}
