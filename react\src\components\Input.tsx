import { useState, useRef, useEffect } from "react";
import { cn } from "../utils/cn";

export default function Input({
  handleGenerate,
  isGenerating,
}: {
  handleGenerate: (prompt: string) => void;
  isGenerating: boolean;
}) {
  const [prompt, setPrompt] = useState("");
  const [wordCount, setWordCount] = useState(0);
  const [charCount, setCharCount] = useState(0);

  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const onInput = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setPrompt(e.target.value);
  };

  const onKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const handleSubmit = async () => {
    if (!prompt.trim() || isGenerating) return;
    handleGenerate(prompt);

    setPrompt("");
    // remove value
    if (textareaRef.current) {
      textareaRef.current.value = "";
    }
  };

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }

    const wordCount = prompt.trim().split(/\s+/).filter(Boolean).length;
    const charCount = prompt.length;
    setWordCount(wordCount);
    setCharCount(charCount);
  }, [prompt]);

  return (
    <div
      style={{
        maxWidth: "calc(100% - 32px)",
      }}
      className="dark:bg-black-100 mb-[20px] flex w-[733px] max-w-full flex-col rounded-2xl border-1 border-[#E4E4E7] bg-white px-[14px] pt-[28px] pb-[8px] shadow-[0px_6px_7px_0px_rgba(0,0,0,0.07)] dark:border-[#32363D]"
    >
      <textarea
        ref={textareaRef}
        onInput={onInput}
        onKeyDown={onKeyDown}
        className={cn(
          "custom-scroll-bar h-auto max-h-[208px] w-full resize-none overflow-auto pr-[14px] font-['Inter'] text-base leading-normal font-normal",
          "dark:text-white-100 text-[#000] placeholder:text-black/35 focus-within:outline-none dark:placeholder:text-white/35",
        )}
        placeholder="Enter your sentence to generate"
      ></textarea>
      <div className="inline-flex items-center justify-between self-stretch">
        <div className="flex w-96 items-center justify-start gap-2.5 self-stretch">
          <div className="justify-start font-['Inter'] text-xs leading-normal font-normal text-black/50 dark:text-white/50">
            {`${charCount} character${charCount !== 1 ? "s" : ""} ${wordCount} word${wordCount !== 1 ? "s" : ""}`}
          </div>
        </div>
        <div className="flex items-center justify-start gap-1.5">
          <div className="flex h-[37px] w-[37px] items-center justify-start gap-2.5">
            <button
              type="submit"
              onClick={handleSubmit}
              disabled={isGenerating || !prompt.trim()}
              className={cn(
                "inline-flex h-[37px] w-[37px] flex-col items-center justify-center gap-2.5 overflow-hidden rounded-[20px] p-[3px] text-[#F5F5F5] dark:text-[#1A1D21]",
                isGenerating || !prompt.trim()
                  ? "cursor-not-allowed bg-[#1A1D211A] dark:bg-[#FFFFFF40]"
                  : "bg-black-100 cursor-pointer hover:bg-black dark:bg-white dark:hover:bg-[#EDEDED]",
              )}
            >
              <svg
                width="17.25"
                height="17.25"
                viewBox="0 0 17.25 17.25"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M17.0806 0.415334L0.589753 7.60283C0.297761 7.74209 0.306745 8.15987 0.60323 8.29014L5.06397 10.8103C5.32901 10.9585 5.65694 10.9271 5.88604 10.7294L14.6817 3.14658C14.7401 3.09717 14.8794 3.00283 14.9333 3.05674C14.9917 3.11514 14.9019 3.2499 14.8524 3.3083L7.24268 11.8794C7.03155 12.1175 7.00011 12.4679 7.17081 12.7374L10.0862 17.4138C10.23 17.6968 10.6388 17.6923 10.769 17.4048L17.5882 0.913967C17.7364 0.59053 17.3995 0.2626 17.0806 0.415334Z"
                  fill="currentColor"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
