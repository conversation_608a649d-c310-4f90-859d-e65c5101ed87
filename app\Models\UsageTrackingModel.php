<?php

namespace App\Models;

class UsageTrackingModel extends BaseModel
{
    protected $DBGroup = 'default';

    protected $table = 'tbl_usageTracking';
    protected $tableAlias = 'tbl_usageTracking UT';
    protected $primaryKey = 'app_id';

    protected $allowedFields = ['track_pid', 'session_id', 'user_email', 'ip_address', 'app', 'api_url', 'user_prompt', 'other_prompt', 'ai_response', 'request_raw', 'response_usage', 'response_raw', 'status'];

    // protected $allowedFields = ['track_pid','session_id','user_email','ip_address','app','api_url','user_prompt','other_prompt','response_usage','status'];

    //---------------------------------------------------------------------------------------------
    //  public
    //---------------------------------------------------------------------------------------------

    public function countLimitCriteria($where = [])
    {
        $query = $this->db->table($this->table)
            ->where($where)
            ->countAllResults();

        return $query;
    }

}
