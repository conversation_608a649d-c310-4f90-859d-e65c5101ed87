#!/bin/sh

##--------------------------------
#
# page_chatbot_clientv2 - 2 Steps
#
##--------------------------------
# pwd

##--------------------------------------
# Step1: copy/replace twig file---------
##--------------------------------------
# cd app/Views/twig/btcomponents/
# mkdir chatbot_client_v2
#pwd

dirChatbotV2=app/Views/twig/btcomponents/chatbot_client_v2/

cp vendor/codebay/btcomponents/CHATBOT/client_v2/dist/_base_template_chatbotClientV2.twig $dirChatbotV2


##--------------------------------------
# Step2: replace comment strings--------
##--------------------------------------
sed -i -e 's/<!--##//g' $dirChatbotV2'_base_template_chatbotClientV2.twig'
sed -i -e 's/##-->//g' $dirChatbotV2'_base_template_chatbotClientV2.twig'

##--------------------------------------
# Step3: copy assets files--------------
##--------------------------------------
dirChatbotV2Assets=public/btcomponents/chatbot_client_v2
rm -r $dirChatbotV2Assets
cp -r vendor/codebay/btcomponents/CHATBOT/client_v2/dist/btcomponents/chatbot_client_v2 $dirChatbotV2Assets

echo ""
echo "#--------------------------------------"
echo "# DONE: _base_template_chatbotClientV2 "
echo "#--------------------------------------"