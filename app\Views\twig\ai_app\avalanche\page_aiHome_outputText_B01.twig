{% extends 'base_B_template.twig' %}

{% block head_styles %}
{{ parent() }}
<style>
  body>div {
    text-align: center;
    margin: 20px auto;
  }

  @media only screen and (min-width: 768px) {
    body>div {width:800px}
  }
</style>
<link rel='stylesheet' href='https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css?ver={{ BASE.build }}'>
<link rel='stylesheet' href='https://cdn.form.io/formiojs/formio.full.min.css?ver={{ BASE.build }}'>
<link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/css/toastr.min.css?ver={{ BASE.build }}' />

<style>
#outputTextField {
  color: green
}
</style>
{% endblock %}


{% block head_scripts %}
{{ parent() }}
<script src='https://api.ai-pro.org/ext-app/js/btutil-common-v1.min.js?ver={{ BASE.build }}'></script>
<script src='https://cdn.form.io/formiojs/formio.full.min.js?ver={{ BASE.build }}'></script>
<script type='text/javascript'>
  var txtPrompt = document.getElementById('textPrompt-textPrompt');
  if (txtPrompt == null) {
    txtPrompt = document.getElementById('textareaPrompt-textareaPrompt');
  }

  window.onload = function () {
    var force = 1;
    var btformData = localStorage.getItem("btformData");
    Formio.createForm(document.getElementById('formio'), JSON.parse(btformData), {}).then(function (form) {
      {# console.log(form.components[0].components[0].component); #}
      if (txtPrompt == null) {
        var inputPrompt = form.components[0].components[0].component;
        txtPrompt = document.getElementById(inputPrompt.id+'-'+inputPrompt.key);
      }
      form.on("change", function (e) {
      });
    });
  }
</script>
{% endblock %}




{% block body_content %}
{{ parent() }}
<div style="padding:50px">
  <div id='formio'></div>
  <div ref="component" class="formio-component formio-component-htmlelement formio-component-htmlOutput" id="edo2qtr">
    <p id="outputTextField" ref="html" class="formio-component-htmlelement"></p>
  </div>
</div>
{% endblock %}



{% block body_hidden_content %}
{{ parent() }}
{% endblock %}


{% block body_scripts %}
{{ parent() }}
<script src="{{ BASE.assets_common }}js/jquery-3.5.1.min.js?ver={{ BASE.build }}"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js?ver={{  BASE.build }}"></script>

<script type='text/javascript'>

  var cbSubmit = generate;


  $(document).ready(function () {
    //
  });

  $(document).keypress(function(event){
    var keycode = (event.keyCode ? event.keyCode : event.which);
    if(keycode == '13'){
      {# generate(); #}
    }
  });


  function generate() {
    if ($('.btn, .btn-success').prop('disabled')) {
      return;
    }

    toastr.options = {
      "closeButton": true,
      "debug": false,
      "newestOnTop": true,
      "progressBar": true,
      "positionClass": "toast-top-center",
      "preventDuplicates": true,
      "onclick": null,
      "showDuration": "300",
      "hideDuration": "1000",
      "timeOut": "10000",
      "extendedTimeOut": "2000",
      "showEasing": "swing",
      "hideEasing": "linear",
      "showMethod": "fadeIn",
      "hideMethod": "fadeOut"
    }
    toastr.clear();
    toastr.info("Processing...", "");

    var url = '{{ BASE.base_url }}api/openai/v1/text-completion';

    var outputContainer = document.getElementById('outputTextField');
    outputContainer.innerHTML = "...";

    var formData = new FormData();
    formData.append('prompt', txtPrompt.value);
    formData.append('slug', 'custom');

    fetch(url, { method: 'POST', body: formData })
    .then(response => {
      return response.json();
    })
    .then((output) => {
      toastr.options.progressBar = false;
      toastr.options.timeOut = 1000;
      toastr.clear();
      if (output.success) {
        toastr.success("", "Success");
        outputContainer.innerHTML = output.data;
      } else {
        toastr.options.hideDuration = 3000;
        toastr.error(output.message, "");
        outputContainer.innerHTML = "...";
      }
    })
    .catch(error => {
      // handle the error
      console.error(error);
    });
  }

  $('#textfield-prompt').keyup(function(event) {
    if (event.keyCode === 13) {
      {# generate(); #}
    }
  });



</script>
{% endblock %}