.fa,.fab,.fad,.fal,.far,.fas {
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1
}
.fa,.far,.fas {
    font-family: "Font Awesome 5 Free"
}

.fa,.fas {
    font-weight: 900
}

#send_doc h5 {
    width: 100%;
    background: #30689b;
    color: #fff;
    font-size: 14px;
    padding: 12px;
    margin-top: 0;
    width: 100%
}

#send_doc .modal-header {
    padding: 0
}

#send_doc .modal_head_btns {
    position: absolute;
    right: 10px;
    top: 10px
}
body.editor-s4s {
    display: contents
}

#send_doc .close {
    padding: 20px
}

#send_doc label {
    background: 0 0;
    text-align: left;
    display: block;
    font-weight: 600
}

#send_doc input,#send_doc textarea {
    width: 100%;
    margin: 0
}

#send_doc .send_docu button {
    width: 170px;
    padding: 5px;
    margin: 0 15px 10px;
    border: none;
    background: #28a745;
    color: #fff
}

#send_doc .send_docu {
    text-align: right
}

#send_doc .from_name span {
    text-align: left!important;
    font-size: 12px
}

#send_doc input.agreesign {
    width: 20px!important;
    height: 12px;
    vertical-align: middle
}
*,::after,::before {
    -webkit-user-select: none;
    -webkit-user-drag: none;
    -webkit-app-region: no-drag
}
body,html {
    height: 100%;
    margin: 0;
    color: #434343
}
.modal-header {
    color: #30689b;
    border: none
}
.modal-header .close {
    color: #fff;
    text-shadow: none!important;
    opacity: 1;
    display: contents
}
h5.modal-title {
    font-size: 15px;
    padding-top: 2px;
    margin: 0
}

h4.modal-title {
    margin-bottom: 0;
    line-height: 1.5;
    width: 100%;
    text-align: center;
    display: block;
    padding-left: 5px
}
.modal-body {
    padding: 1rem
}

.modal input {
    width: 80%;
    margin: 10px;
    background: #f4f4f4;
    border: 1px solid #dee2e6;
    height: 35px;
    font-size: 14px;
    padding: 10px
}

.modal .tab-content {
    text-align: center
}
label {
    color: #000;
    padding: 7px;
    margin: 3px;
    border-radius: 5px;
    font-size: 15px;
    background: #fff;
    background: -moz-linear-gradient(top,#fff 0,#ededed 100%);
    background: -webkit-gradient(left top,left bottom,color-stop(0,#fff),color-stop(100%,#ededed));
    background: -webkit-linear-gradient(top,#fff 0,#ededed 100%);
    background: -o-linear-gradient(top,#fff 0,#ededed 100%);
    background: -ms-linear-gradient(top,#fff 0,#ededed 100%);
    background: linear-gradient(to bottom,#fff 0,#ededed 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#ededed', GradientType=0 )
}


@media screen and (min-width: 576px) {
    #send_doc .modal-dialog {
        max-width:700px
    }
}

