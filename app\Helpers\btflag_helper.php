<?php
if (!function_exists('btflag')) {
    function btflag($flag, $default = null)
    {
        $flag_is = btflag_protected($flag, $default);
        if ($flag_is) {
            btflag_set($flag, $flag_is);
            return $flag_is;
        }
        $flag_is = $default;
        if (isset($_GET[$flag])) {
            $flag_is = !empty($_GET[$flag]) ? $_GET[$flag] : $default;
            btflag_set($flag, $flag_is);
            return $flag_is;
        }
        return isset($_COOKIE[$flag]) ? $_COOKIE[$flag] : $flag_is;
    }
}
if (!function_exists('btflag_xlate')) {
    function btflag_xlate($flag, $default = null)
    {
        $flag_is = btFlag($flag, $default);
        $protected_flag = btflag_protected_flags();
        return $protected_flag[$flag]['xlate'][$flag_is];
    }
}
if (!function_exists('btflag_protected_flags')) {
    function btflag_protected_flags()
    {
        return [
            'mode' => [
                'default' => 'live',
                'xlate' => ['' => 'Live', 'live' => 'Live', 'test' => 'Test'],
                'path' => '/',
                'domain' => '',
            ],
        ];
    }
}
if (!function_exists('btflag_protected')) {
    function btflag_protected($flag, $default = null)
    {
        $protected_flag = btflag_protected_flags();
        if (!array_key_exists($flag, $protected_flag)) {
            return false;
        }
        if ($default == null) {
            $default = $protected_flag[$flag]['default'];
        }
        $flag_is = $default;
        $flag_hashed = btflag_hash($flag);
        if (isset($_GET[$flag])) {
            $flag_value = $_GET[$flag];
            if ($flag_value == $default) {
                btflag_remove($flag_hashed, 1);
                return $default;
            }
            $flag_is = getenv($flag_value);
            if ($flag_is == "") {
                btflag_remove($flag_hashed, 1);
                return $default;
            }
            btFlag_set($flag_hashed, 1);
            return $flag_is;
        }
        if (!isset($_COOKIE[$flag_hashed])) {
            btflag_remove($flag, 1);
            btflag_remove($flag_hashed, 1);
            return '';
        }
        return !empty($_COOKIE[$flag]) ? $_COOKIE[$flag] : $default;
    }
}
if (!function_exists('btflag_set')) {
    function btflag_set($flag, $value, $option = [])
    {
        $path = isset($option['path']) ? $option['path'] : '/';
        $domain = isset($option['domain']) ? $option['domain'] : '';
        return setcookie($flag, $value, time() + (86400 * 30), $path, $domain);
    }
}
if (!function_exists('btflag_remove')) {
    function btflag_remove($flag, $value, $option = [])
    {
        $path = isset($option['path']) ? $option['path'] : '/';
        $domain = isset($option['domain']) ? $option['domain'] : '';
        return setcookie($flag, $value, time() - (86400 * 30), $path, $domain);
    }
}
if (!function_exists('btflag_hash')) {
    function btflag_hash($flag)
    {
        $hashing = '5_UGy_';
        $prefix = '__';
        return $prefix . hash('md5', $hashing . $flag, false);
    }
}
if (!function_exists('btflag_remove_protected_flags')) {
    function btflag_remove_protected_flags()
    {
        $protected_flag = btflag_protected_flags();
        foreach ($protected_flag as $name => $value) {
            btflag_remove($name, '');
            btflag_remove($name, '', $value['path'], $value['domain']);
        }
        $flag_hashed = btflag_hash('mode');
        $other_flag = [
            $flag_hashed,
        ];
        foreach ($other_flag as $key => $name) {
            btflag_remove($name, '');
        }
    }
}
if (!function_exists('btflag_get')) {
    function btflag_get($flag, $value = null)
    {
        return (isset($_GET[$flag])) ? $_GET[$flag] : $value;
    }
}
if (!function_exists('btflag_cookie')) {
    function btflag_cookie($flag, $value = null)
    {
        return (isset($_COOKIE[$flag])) ? $_COOKIE[$flag] : $value;
    }
}
if (!function_exists('btflag_init')) {
    function btflag_init()
    {
        $protected_flag = btflag_protected_flags();

        foreach ($protected_flag as $key => $value) {
            $flag_value = (isset($_COOKIE[$key])) ? $_COOKIE[$key] : $value['default'];
            $flag_value = (isset($_GET[$key])) ? $_GET[$key] : $flag_value;
            btflag_set($key, $flag_value);
        }
    }
}
if (!function_exists('btflag_default')) {
    function btflag_default($flag)
    {
        $protected_flag = btflag_protected_flags();
        $value = isset($protected_flag[$flag]) && isset($protected_flag[$flag]['default']) ? $protected_flag[$flag]['default'] : '';
        return $value;
    }
}
