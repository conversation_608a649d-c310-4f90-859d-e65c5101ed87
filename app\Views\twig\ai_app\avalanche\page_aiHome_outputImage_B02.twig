{% extends 'base_A_template.twig' %}

{% block head_styles %}
{{ parent() }}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css" integrity="sha512-5A8nwdMOWrSz20fDsjczgUidUBR8liPYU+WymTZP1lmY9G6Oc7HlZv156XqnsgNUzTyMefFTcsFH/tnJE/+xBg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<style>
  @import url("https://fonts.googleapis.com/css2?family=Alegreya+Sans:wght@100;300;400;500;700;800;900&display=swap");

  body>div {text-align: center; width: 100%;}
  h2.formio-component-htmlelement {font-size: 1.5rem !important;}
  .fieldset-body { font-size: 14px;}
  #positive-prompt, #negative-prompt {text-align:left !important;}
  ::-webkit-input-placeholder {opacity: 0.5 !important;}
  ::-moz-placeholder {opacity: 0.5 !important;}
  :-ms-placeholder {opacity: 0.5 !important;}
  ::placeholder {opacity: 0.5 !important}
  .container_field { display: inline-block; width: 50%; padding: 10px;}
  #negative-prompt-textArea, #positive-prompt-textArea {height: 35px;}
  .form-control {font-size: 0.8rem !important;}
  .form-group {margin-bottom: 0.2rem !important;}
  .art_cont {padding: 50px 10px; color: #7d7d7d;}
  .formio-component-submit {margin-top: 10px;}
  #modal-container {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
  }
  .btn-success[disabled],
  .btn-success.disabled {
    cursor: not-allowed;
  }
  .btn-success {
    font-weight: bold !important;
  }
  #modal-content {
    position: absolute;
    top: 44%;
    left: 50%;
    transform: translate(-50%,-50%);
    max-width: 90%;
    background-color: #0038ff;
    border-radius: 7px;
    width: 500px;
    padding: 30px;
    text-align: center;
    color: #fff;
    box-shadow: 0 5px 18px -7px black;
    font-family: 'Alegreya Sans',sans-serif!important;
    background-image: url("{{ BASE.assets_common }}images/bg.png");
  }

  div#modal-subtitle {
      margin: 5px 0 10px 0;
  }

  #modal-title {
    font-size: 24px;
    margin: 15px 0 20px;
    padding: 0 20px;
  }

  #modal-desc {
    font-size: 16px;
    margin-bottom: 30px;
  }

  #modal-desc span {
    font-size: 20px;
    font-weight: bold;
  }

  #modal-cta {
    display: block;
    margin: 0 auto;
    font-size: 18px;
    background-color: #fff;
    color: #052f5e;
    border: none;
    border-radius: 30px;
    padding: 10px;
    cursor: pointer;
    text-decoration: none;
    width: 50%;
    box-shadow: 0 3px 10px -4px black;
    font-weight: bold;
  }

  .suggestion {
    margin-top: 25px
  }

  .suggestion-container p{
    font-size: 12px;
    font-weight: bold;
  }
  .suggestion-items-container{
    text-align: initial;
  }
  .item{
    display: inline-block;
    padding: 2px 15px;
    font-size: 12px;
    border: 1px solid #6D6D6D;
    border-radius: 15px;
    color: #6D6D6D;
    margin: 0 5px 7px 0;
    cursor: pointer;
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* IE10+/Edge */
    user-select: none; /* Standard */
  }
  .item.active{
    color: #fff;
    background: #6D6D6D;
  }
  .suggestion-container.container,
  .suggestion-container .col-md-3 {
    padding-left: 0;
  }

  .img-container {
    display: inline-block;
    position: relative;
    overflow: hidden;
  }
  .img-container > a > div { overflow: hidden; }
  .img-container.blur-img img {
    filter: blur(20px);
  }
  .img-container:not(.blur-img) .view-img { display: none; }
  .view-img {
    position: absolute;
    top: calc(50% - 8.5px);
    left: calc(50% - 60px);
    cursor: pointer;
    background: #ffffff61;
    padding: 5px 15px;
    border-radius: 5px;
  }

  @media (max-width: 769px) {
    .suggestion-container {float: left; padding-left:15px !important;}
    .suggestion-container p{
      text-align: left;
    }
  }

  @media (max-width: 767px) {
    #modal-content {
      max-width: 90%;
      padding: 20px;
    }
    #modal-title {
        font-size: 26px;
        margin: 15px 0 20px;
        padding: 0;
    }
  }
  @media (max-width: 517px) {
    .img-container img {
      width: 100%;
    }
  }
  @media (max-width: 500px) {
    a.btn {
        font-size: 12px;
        padding: 5px 0;
    }
  }
/*  @media only screen and (min-width: 768px) {
    body>div {width:800px}
  }*/

  .lb-data .lb-close {
  position: absolute;
  top: 7px;
  margin-left: 0px;
  z-index: 99;
}
#lightboxOverlay {
  max-height: 100vh;
  max-width:100%;
}

#lightbox {
  top:0 !important;
}

#lightbox .lb-outerContainer {
  height: calc(100vh - 5px)!important;
  background: transparent;
  display: flex;
  justify-content: center;
}

#lightbox .lb-outerContainer img {
  width: auto !important;
  height:100% !important;
}
</style>
<link rel='stylesheet' href='https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css'>
<link rel='stylesheet' href='https://cdn.form.io/formiojs/formio.full.min.css'>
<link rel="preload" as="image" href="{{ BASE.assets_common }}images/bg.png" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.4/css/lightbox.min.css" integrity="sha512-ZKX+BvQihRJPA8CROKBhDNvoc2aDMOdAlcm7TUQY+35XYtrd3yh95QOOhsPDQY9QnKE0Wqag9y38OIgEvb88cA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
{% endblock %}


{% block head_scripts %}
{{ parent() }}
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src='https://cdn.form.io/formiojs/formio.full.min.js?ver={{ BASE.build }}'></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.4/js/lightbox-plus-jquery.min.js" integrity="sha512-U9dKDqsXAE11UA9kZ0XKFyZ2gQCj+3AwZdBMni7yXSvWqLFEj8C1s7wRmWl9iyij8d5zb4wm56j4z/JVEwS77g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

<script type='text/javascript'>

  window.onload = function () {

    var force = 1;
    var builder_template = {
      "label": "Container",
      "tableView": false,
      "key": "container",
      "type": "container",
      "input": true,
      "components": [
        {
          "label": "HTML",
          "tag": "h1",
          "attrs": [
            {
              "attr": "",
              "value": ""
            }
          ],
          "content": "DreamPhoto",
          "refreshOnChange": false,
          "key": "html",
          "type": "htmlelement",
          "input": false,
          "tableView": false
        },
        {
          "key": "fieldSet",
          "type": "fieldset",
          "label": "Field Set",
          "input": false,
          "tableView": false,
          "components": [
            {
              "label": "<b role='contentinfo' aria-label='Positive prompt'>Positive prompt:</b> Detailed descriptions of the image you want to create",
              "placeholder": "A really cute cat, running through the snow, wearing pink shoes",
              "autoExpand": false,
              "tableView": true,
              "key": "positivePromptArea",
              "type": "textarea",
              "input": true,
              "id": "positive-prompt"
            },
            {
              "label": "<b role='contentinfo' aria-label='Negative prompt'>Negative prompt:</b> Detailed descriptions of the things you do not want to see in your image",
              "placeholder": "Extra limbs, extra fingers, weird eyes, fangs, and alien-looking body",
              "autoExpand": false,
              "tableView": true,
              "key": "negativePromptArea",
              "type": "textarea",
              "input": true,
              "id": "negative-prompt"
            },
            {
              "label": "Generate Image",
              "id": "generate-image",
              "action": "custom",
              "showValidations": true,
              "theme": "success",
              "block": true,
              "disableOnInvalid": true,
              "tableView": false,
              "key": "submit",
              "type": "button",
              "custom": "generate();",
              "input": true
            },
            {
              "label": "HTML",
              "attrs": [
                {
                  "attr": "id",
                  "value": "outputProperEnglish"
                }
              ],
              "refreshOnChange": false,
              "key": "htmlOutput",
              "type": "htmlelement",
              "input": false,
              "tableView": false
            }
          ]
        }
      ]
    }

    builder_template = JSON.stringify(builder_template);

    Formio.createForm(document.getElementById('formio'), JSON.parse(builder_template), {})
    .then(function (form) {
      const positivePromptArea = document.querySelector('.formio-component-positivePromptArea');
      if (positivePromptArea) {
        const positivePromptLabel = positivePromptArea.querySelector('label');
        positivePromptLabel.setAttribute('role', 'contentinfo')
        positivePromptLabel.setAttribute('aria-label', 'Positive prompt')
      }

        const negativePromptArea = document.querySelector('.formio-component-negativePromptArea');
      if (negativePromptArea) {
        const negativePromptLabel = negativePromptArea.querySelector('label');
        negativePromptLabel.setAttribute('role', 'contentinfo')
        negativePromptLabel.setAttribute('aria-label', 'Negative prompt')
      }

    });

    const positivePrompt = document.querySelector('textarea[id*="-positivePromptArea"]');
    const newDiv = document.createElement("div");
    newDiv.classList.add('suggestion');

    newDiv.innerHTML = `
      <div class="suggestion-container container">
        <div class="row">
          <div class="col-md-3">
            <p   aria-label='Suggested Prompts'>Suggested Prompts for:</p>
          </div>
          <div class="suggestion-items-container col-md-9">
            <div class="sugg-1 item" tabindex="0" role='button' aria-label='Portrait'>Portrait</div>
            <div class="sugg-2 item" tabindex="0" role='button' aria-label='Scenic View'>Scenic View</div>
            <div class="sugg-3 item" tabindex="0" role='button' aria-label='Food'>Food</div>
            <div class="sugg-4 item" tabindex="0" role='button' aria-label='Animal'>Animal</div>
            <div class="sugg-5 item" tabindex="0" role='button' aria-label='Vehicle'>Vehicle</div>
            <div class="sugg-6 item" tabindex="0" role='button' aria-label='Architecture'>Architecture</div>
            <div class="sugg-7 item" tabindex="0" role='button' aria-label='Monster'>Monster</div>
            <div class="sugg-8 item" tabindex="0" role='button' aria-label='Anime'>Anime</div>
          </div>
        </div>
      </div>
    `;
    positivePrompt.insertAdjacentElement("afterend", newDiv);

  }
</script>
{% endblock %}



{% block body_content %}
  {{ parent() }}
  <div id="txt2image" style="padding: 20px">
      <div id='formio'></div>
      <div id="usageCounter"></div>
  </div>

  <!--MODAL-->
  <div id="modal-container" style="display: none;">
    <div id="modal-content">
      <div id="modal-subtitle"></div>
      <h2 id="modal-title"></h2>
      <p id="modal-desc"></p>
      <a id="modal-cta" href="#" target="_parent"></a>
    </div>
  </div>
{% endblock %}


{% block body_footer %}
{{ parent() }}
{% endblock %}



{% block body_hidden_content %}
{{ parent() }}
{% endblock %}


{% block body_scripts %}
{{ parent() }}
<script src="{{ BASE.assets_common }}js/jquery-3.5.1.min.js?ver={{ BASE.build }}"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js?ver={{  BASE.build }}"></script>

<script type='text/javascript'>

  const showAgeConfirmation = (fn = ()=>{}) => {
    Swal.fire({
      title: "Confirmation Required",
      html: "Please confirm that you are 18 years old or above.",
      showCancelButton: true,
      confirmButtonText: "Confirm",
      confirmButtonColor: "#16a34a"
    }).then((result) => {
      if (result.isConfirmed) {
        fn();
      }
    });
  }

  var isLegalAge = false;
  var cbSubmit = generate;

  function generate() {
    // if ($('.btn, .btn-success').prop('disabled')) {
    //   return;
    // }
    var genbtn = document.querySelector('.btn-success');

    if (genbtn.disabled) {
      return;
    }

    setTimeout(function() {
      genbtn.disabled = true;
    }, 500);

    var usageCounter = document.getElementById('usageCounter');
    toastr.options = {
      "closeButton": true,
      "debug": false,
      "newestOnTop": true,
      "progressBar": true,
      "positionClass": "toast-top-center",
      "preventDuplicates": true,
      "onclick": null,
      "showDuration": "300",
      "hideDuration": "1000",
      "timeOut": "10000",
      "extendedTimeOut": "2000",
      "showEasing": "swing",
      "hideEasing": "linear",
      "showMethod": "fadeIn",
      "hideMethod": "fadeOut"
    }

    toastr.clear();
    toastr.info("Processing...", "");

    var url = '{{ BASE.base_url }}/api/ai/v2/text-to-image';

    var slug = '{{ PAGE.slug }}';
    var surfToken = '{{ PAGE.surfToken.hash }}';
    var positivePrompt = document.querySelector('textarea[id*="-positivePromptArea"]');
    var negativePrompt = document.querySelector('textarea[id*="-negativePromptArea"]');
    var inputPrompt = document.getElementById('prompt-textArea');
    var outputContainer = document.getElementById('outputProperEnglish');
    outputContainer.innerHTML = '<div class="d-flex justify-content-center art_cont"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>';

    outputContainer.scrollIntoView({ behavior: 'smooth', block: 'end'});

    var formData = new FormData();
    var counter = 0;
    var prompt = ({positive: positivePrompt.value, negative: negativePrompt.value});
    formData.append('prompt', JSON.stringify(prompt));
    formData.append('slug', slug);
    formData.append('surfToken', surfToken);

    fetch(url, { method: 'POST', body: formData })
    .then(response => {
      return response.json();
    })
    .then((output) => {
      toastr.options.progressBar = false;
      toastr.options.timeOut = 1000;
      toastr.clear();

      if (output.success == 2) {
        const modalsubTitle = document.querySelector('#modal-subtitle');
        const modalTitle = document.querySelector('#modal-title');
        const modalDesc = document.querySelector('#modal-desc');
        const modalCta = document.querySelector('#modal-cta');

        modalsubTitle.innerHTML = output.modal.subTitle;
        modalTitle.innerHTML = output.modal.title;
        modalDesc.innerHTML = output.modal.desc;
        modalCta.innerHTML = output.modal.ctaLabel;
        modalCta.href = output.modal.redirectUrl;

        const modalContainer = document.querySelector('#modal-container');
        modalContainer.style.display = 'block';
        return;
      }

      if (output.success) {
        toastr.success("", "Success");
        var htmlOutput = '';
        for (var i = 0; i < output.data.images.length; i++) {
            if (output.data.blur && output.data.blur[i]) {
              htmlOutput += `
                <div class="img-container blur-img nsfw">
                  <a>
                    <img src="data:image/png;base64,${output.data.images[i]}" alt="">
                  </a>
                  <a class="view-img"><i class="fa fa-eye" aria-hidden="true"></i> View Image</a>
                </div>
              `;
            } else {
              htmlOutput += `
                <div class="img-container">
                  <a href="data:image/png;base64,${output.data.images[i]}" data-lightbox="text-to-image-${[i]}">
                    <img src="data:image/png;base64,${output.data.images[i]}" alt="">
                  </a>
                </div>
              `;
            }
        }
        outputContainer.innerHTML = htmlOutput;

        $('.img-container.nsfw a').on('click', function(e){
          if( !$(this).closest(".img-container").hasClass("blur-img") ) {
            $(this).closest(".img-container").addClass("blur-img")
            return;
          }
          e.preventDefault();
          let vm = this;
          let fn = () => {
            $(vm).closest('.blur-img').removeClass('blur-img');
          }
          if(isLegalAge) {
            fn();
            return;
          }
          showAgeConfirmation(() => {
            isLegalAge = true;
            fn();
          });
        });

        $('.img-container').each(function () {
          $(this).addClass('img-fluid');
          $(this).append('<div class="mt-2 mb-3 col-xs-1 download-img" align="center"><a style="max-width:512px;" href="'+$(this).find('img').attr('src')+'" download="download" class="btn btn-primary btn-md btn-block active" role="button" aria-pressed="true">DOWNLOAD</a><hr></div>');
        });
        setTimeout(function() {
          document.getElementById('outputProperEnglish').scrollIntoView({ behavior: 'smooth', block: 'end'});
        }, 1000);
        genbtn.disabled = false;

      } else {
        if(output.message !== 'OK'){
          outputContainer.innerHTML = '<div class="alert alert-danger">'+ output.message +'</div>';
          genbtn.disabled = false;
        }else{
        toastr.options.hideDuration = 3000;
        toastr.error(output.message, "");
        outputContainer.innerHTML = '<div class="d-flex justify-content-center art_cont" align="center">We apologize for the inconvenience, but we are currently experiencing high traffic and are unable to generate your image at this time. Please try again later, and thank you for your patience.</div>';
        genbtn.disabled = false;
        }
      }

    })
    .catch(error => {
      // handle the error
      console.error(error);
      toastr.options.hideDuration = 3000;
      toastr.error("Failed to load data", "");
      outputContainer.innerHTML = '<div class="d-flex justify-content-center art_cont" align="center">We apologize for the inconvenience, but we are currently experiencing high traffic and are unable to generate your image at this time. Please try again later, and thank you for your patience.</div>';
        genbtn.disabled = false;
    });

    //trigger when user clicks Generate Image button
    //TrustPilot logic change
    const TPLogicRun = window.TPLogicRun;
    console.log("TPLogicRun", typeof TPLogicRun);
    if (typeof TPLogicRun === 'function') {
      TPLogicRun();
    } else {
      console.error("TrustPilot TPLogicRun function failed to execute");
    }
  }

  $(document).on('click', '.blur-img .download-img a', function(e){
    if( isLegalAge ) return;
    e.preventDefault();
    let vm = this;
    let fn = () => {
      let url = $(vm).attr('href');
      const a = document.createElement('a');
      a.href = url;
      a.download = $(vm).attr('download');
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    };
    showAgeConfirmation(() => {
      isLegalAge = true;
      fn();
    });
  });

  $(document).ready(function () {
    function  suggestedPromptsEventHandler(event) {
        const positivePrompt = document.querySelector('textarea[id*="-positivePromptArea"]');
        const negativePrompt = document.querySelector('textarea[id*="-negativePromptArea"]');
        const target = event.target;
        if (target.classList.contains('item')) {
          const itemText = target.innerText;
          const suggestionData = {
            'items': [
              {
                'text': 'Portrait',
                'positivePrompt': [
                  'Close up portrait of a white-haired old man, Exquisite detail, 30-megapixel, 4k, 85-mm-lens, sharp-focus, f:8, ISO 100, shutter-speed 1:125, diffuse-back-lighting, award-winning photograph, small-catchlight, High-sharpness, facial-symmetry',
                  'Closeup portrait of a cyborg, mechanical parts, ultra realistic, concept art, intricate details, eerie, highly detailed, photorealistic, 8k, unreal engine. art by artgerm and greg rutkowski and charlie bowater and magali villeneuve and alphonse mucha, golden hour, cyberpunk, robotic, steampunk, neon colors, metallic textures.',
                  'Professional portrait photograph of a gorgeous Norwegian girl in winter clothing with long wavy blonde hair, ((sultry flirty look)), freckles, beautiful symmetrical face, cute natural makeup, ((standing outside in snowy city street)), stunning modern urban upscale environment, ultra realistic, concept art, elegant, highly detailed, intricate, sharp focus, depth of field, f/1. 8, 85mm, medium shot, mid shot, (centered image composition), (professionally color graded), ((bright soft diffused light)), volumetric fog, trending on instagram, trending on tumblr, hdr 4k, 8k'
                ],
                'negativePrompt':[
                  '(bonnet), (hat), (beanie), cap, (((wide shot))), (cropped head), bad framing, out of frame, deformed, cripple, old, fat, ugly, poor, missing arm, additional arms, additional legs, additional head, additional face, multiple people, group of people, dyed hair, black and white, grayscale'
                ]
              },
              {
                'text': 'Scenic View',
                'positivePrompt': [
                  'View of the northern lights at night time, seen in Alaska, Canon RF 16mm f:2.8 STM Lens, hyperrealistic photography, style of Unsplash and National Geographic',
                  'Sea of Endless waves with the sparkling reflection of the sun, golden hour, Canon RF 16mm f:2.8 STM Lens,  hyperrealistic photography, style of unsplash and National Geographic',
                  'Hills of the Scotland highlands, misty fog, Canon RF 16mm f:2.8 STM Lens, award winning photography, by National Geographic and Unsplash'
                ],
              },
              {
                'text': 'Food',
                'positivePrompt': [
                  'sliced ​​rib-eye steak on a red plate, with separate stir-fry vegetables and extra fried potatoes in a bowl, on a steel table + 4k resolution + photorealistic, ambient light  --beta --upbeta',
                  'very healthy food plate with avocados, tomatoes, eggs and other healthy food, 8 k resolution, food photography, studio lighting, sharp focus, hyper - detailed',
                  'royal fast food, burger, fries, coffee, nuggets, salad, steam coming out, hyper detailed, intricate photorealistic, 8k, UHD, delicious, amazing, epic, epic contour light, volumetric light --v 4 --upbeta'
                ],
              },
              {
                'text': 'Animal',
                'positivePrompt': [
                  'majestic Lion, mane made of fire, illuminating the surrounding area creating beautiful stunning shadows around it and over the Lion’s head.  painted portrait ,fantasy, intricate, elegant, highly detailed, digital painting, artstation, concept art, smooth, sharp focus, illustration, art by gaston bussiere and alphonse mucha',
                  'photography print of taxidermy Whitehead Eagle, spread wings, majestic, stuffing of twigs and moss falling out, next on a cliff background, dappled lighting, backlit, by ellen jewett and Dariusz Zawadzki and Brian froud and tom bagshaw, real, realistic, 8k, high resolution, high definition, wildlife photography --ar 5:3 --s 3500',
                  'a shark themed mecha walking on the water, diffuse lighting, fantasy, highly detailed, photorealistic, digital painting, artstation, illustration, concept art, smooth, sharp focus'
                ],
              },
              {
                'text': 'Vehicle',
                'positivePrompt': [
                  'concept art of motorcycle, highly detailed painting by dustin nguyen, akihiko yoshida, greg tocchini, greg rutkowski, cliff chiang, 4 k resolution, trending on artstation, 8k',
                  'a concept spaceship made out of Tesla parts, Ferrari parts, F1 racing parts :: x-wing, concept art, art station trends, airbase setting, space station setting, unreal engine render, octane render, 4k, volumetric lighting, photorealistic, new type, sleek design, ultra aerodynamic, spacecraft, dynamic style, F-22 fighter jet, concept car, stealth bomber, wide angle --ar 5:3',
                  'sport car, shimmery metallic blue job paint, aggressive look, tuned, bmw and corvette mix, full car only, in motion, (bright headlights on:1.6), (at night:1.6), high speed, (motion blur:1.3), (driver:1.6), movie action scene, (Need for Speed:1.3), wet road, illegal racing game, (industrial cityscape in background:1.5), (detailed stunning environment:1.5), (foggy), moody dark atmosphere, bright headlights, neon underground aesthetics, (sci-fi), cyberpunk, blade runner, cinematic, cover art, (low front angle), full view of a sports car, intricate, highly detailed, digital painting, digital art, artstation, concept art, (complementary colors:1.5), (color contrast:1.5), best quality masterpiece, photorealistic, detailed, sharp focus, 8k, HDR, shallow depth of field, broad light, high contrast, backlighting, bloom, light sparkles, chromatic aberration, sharp focus, RAW color photo'
                ],
              },
              {
                'text': 'Architecture',
                'positivePrompt': [
                  'house, golden hour, Canon RF 16mm f:2.8 STM Lens,  hyperrealistic photography, style of unsplash and National Geographic',
                  'a lush apartment building covered in vines by studio ghibli sticker:: a die cut studio ghibli sticker of an apartment building in a lush jungle city --ar 9:16',
                  'A man smoking in the balcony of his condominium surrounded by windows with lush vegetation, wooden floor, high ceiling, beige blue salmon pastel palette, interior design magazine, cozy atmosphere'
                ],
              },
              {
                'text': 'Monster',
                'positivePrompt': [
                  'Closeup portrait of a monster with glowing eyes and sharp teeth, dark shadows, foggy background, highly detailed, photorealism, concept art, digital painting, art by yahoo kim, max grecke, james white, viktor hulík, fabrizio bortolussi.',
                  'a humanoid monster made of metal and flensed muscle , flensed muscle, gears, monster concept art, monster art, --c 100 --ar 3:4',
                  'A seamless pattern of surreal colorful fluffy elaborate and detailed futuristic monsters, complicated surreal monsters, fun patterns, detailed accents, monster details, colorful --v4'
                ],
              },
              {
                'text': 'Anime',
                'positivePrompt': [
                  'game 2d art, image of a young man with light gray hair, 16 years old, green eyes, white tank top, steampunk clothes, anime, anime anime anime anime anime, dynamic pose, ultra-detailed, white background, HD, design art by Hideo Minaba, Yuya Nagai, Ryoji Ohara, Ryota Murayama and Hitomi Yoshimura, granblue fantasy --v4',
                  'game 2d art, image of a young girl with light blonde hair, 14 years old, green eyes, white skirt, anime, anime anime anime anime anime, dynamic pose, ultra-detailed, white background, HD, design art by Hideo Minaba, Yuya Nagai, Ryoji Ohara, Ryota Murayama and Hitomi Yoshimura, granblue fantasy, artgerm --v4'
                ],
              },
            ]
          }
          const suggestion = suggestionData.items.find(item => item.text === itemText);

          if (suggestion) {
            const positivePrompts = suggestion.positivePrompt;
            const negativePrompts = suggestion.negativePrompt || [];

            const randomPositivePrompt = positivePrompts[Math.floor(Math.random() * positivePrompts.length)];
            const randomNegativePrompt = negativePrompts.length > 0 ? negativePrompts[Math.floor(Math.random() * negativePrompts.length)] : '';
            const activeItems = document.querySelectorAll('.item.active');
            activeItems.forEach(function(item) {
              item.classList.remove('active');
            });

            target.setAttribute('data-positive-prompt', randomPositivePrompt);
            target.setAttribute('data-negative-prompt', randomNegativePrompt);

            const positivePrompt = target.getAttribute('data-positive-prompt');
            const negativePrompt = target.getAttribute('data-negative-prompt');
          }

          if (target.classList.contains('active')){
            positivePrompt.value = '';
            negativePrompt.value = '';
          } else {
            target.classList.add("active");
            positivePrompt.value = target.getAttribute('data-positive-prompt');
            negativePrompt.value = target.getAttribute('data-negative-prompt');
          }


        }
      }
      document.addEventListener('click',  suggestedPromptsEventHandler);
      document.addEventListener("keypress",  (event) => {
        if (event.key === "Enter") {
          event.preventDefault();
          suggestedPromptsEventHandler(event)
        }
      });

      document.addEventListener('input', function(evt) {
        if ((evt.target.matches('textarea[id*="-positivePromptArea"]')) && (evt.target.value === '')){
          const activeItems = document.querySelectorAll('.item.active');
          activeItems.forEach(function(item){
            item.classList.remove('active');
          });
        }
      });
  });


</script>
{% endblock %}