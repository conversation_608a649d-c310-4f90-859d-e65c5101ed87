<?php

namespace App\Controllers\ArtificialIntelligence;

use App\Controllers\ArtificialIntelligence\_BaseArtificialIntelligence as AIBase;
use App\Models\AppFormsModel;

class Crudl extends AIBase
{

    //---------------------------------------------------------------------------------------------
    //  public
    //---------------------------------------------------------------------------------------------

    public function __construct()
    {
        parent::__construct();
    }



    //POST
    public function createAppForm()
    {
        $request = $this->request->getPost();
        $request = json_decode($request['res'], true);

        // btutilDebug($request);
        $appFormModel = new AppFormsModel();

        $res = $this->appResponse;



        $date_now = date('Y-m-d H:i:s', time());
        $data = [
            'app_pid' => $request['data']['appId'],
            'app_name' => $request['data']['appTitle'],
            'app_description' => $request['data']['appDescription'],
            'created_at' => $date_now
        ];

        // $insert_id = null;
        $insert_id = $appFormModel->insert($data);

        $res = [
            'success' => 1,
            'message' => 'success', // TODO: change to OK
            'data' => [
                'last_id' => $insert_id,
            ]
        ];
        return $this->response->setJSON($res);
    }


    //GET
    public function read() {

    }



    //POST
    public function update()
    {

    }



    //POST
    public function delete()
    {

    }

    //GET
    public function list() {

    }


    //---------------------------------------------------------------------------------------------
    //  private
    //---------------------------------------------------------------------------------------------




    //change to private after debug use
    //TODO: move to Debug Controller
    private function debug()
    {
        $debug = btflag('debug', 0); //not yet used
        // $output = shell_exec('ls -l');
        // echo "<pre>$output</pre>";

        // Session
        // $output = btsessionGet('app');
        $output = btsessionGet('btwizard_builder');
        return $this->response->setXML($output);
        // or
        //return $this->response->setJSON($output);
    }






}
