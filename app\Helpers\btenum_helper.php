<?php

/**
 * btenum
 *
 *
 *
 *
 *
 * @return void
 */
function btenum()
{
    $start_url = getenv('START_URL') ? getenv('START_URL') : START_URL;
    $ctaModal_logic = btflag_cookie('regRedirectWP', '0') === '1' ? WP_URL : $start_url;
    $wp_flow = btflag_cookie('flow', '');
    $register_url = '/register';

    if(btflag_cookie('regRedirectWP', '0') === '1'){
        switch ($wp_flow) {
            case '02':
                $register_url = '/register-b';
            break;
            case '03':
                $register_url = '/register-x';
            break;
            case '04':
                $register_url = '/select-account-type-d'; //splash
            break;
            case '05':
                $register_url = '/aurora-register';
            break;
            default:
                $register_url = $register_url;
        }
    }

    $enum = [
        'forms' => [
            'blank-letter-1p.pdf' => (object) [
                'filenameToken' => '04809772d41cbc6b6bafcfa1f4603a85',
            ],
        ],
        'restrictionsProtectedAccess' => [
            'chatbot' => [
                "slug_access" => [
                    'restriction_code' => isPRIVATE,
                    'rules' => [],
                ],
            ],
            'start-chatbot' => [
                "slug_access" => [
                    'restriction_code' => isPROTECTED,
                    'rules' => [
                        'user_prompts' => [
                            'condition' => 'count_limit',
                            'value' => btutilStripNonNumChars(btflag('WcvYPABR', FLAG_DYNAMIC_PROMPT)),
                            'response' => [
                                'success' => 2,
                                'message' => 'Restricted.',
                                'data' => 'Register to Continue Using ChatGPT.',
                                'modal' => [
                                    'title' => 'Register to Continue Using ChatGPT',
                                    'subTitle' => 'Enjoying ChatGPT so far?',
                                    'desc' => 'Write emails, essays, poetry, answer questions, and generate code',
                                    'ctaLabel' => 'CONTINUE',
                                    'redirectUrl' => $ctaModal_logic . $register_url,
                                ],
                            ]
                        ],
                    ],
                ],
            ],
            'text-to-image' => [
                "slug_access" => [
                    'restriction_code' => isPROTECTED,
                    'rules' => [
                        'user_prompts' => [
                            'condition' => 'count_limit',
                            'value' => btutilStripNonNumChars(btflag('WcvYPABR', FLAG_DYNAMIC_PROMPT)),
                            'response' => [
                                'success' => 2,
                                'message' => 'Restricted.',
                                'data' => 'Register to Continue Using Text to Image.',
                                'modal' => [
                                    'title' => 'Register to Continue Using Text to Image',
                                    'subTitle' => 'Enjoying Text to Image so far?',
                                    'desc' => 'Generate hundreds of images!',
                                    'ctaLabel' => 'CONTINUE',
                                    'redirectUrl' => $ctaModal_logic . $register_url,
                                ],
                            ]
                        ],
                    ],
                ],
            ],
            'convert-to-proper-english' => [
                "slug_access" => [
                    'restriction_code' => isPROTECTED,
                    'rules' => [
                        'user_prompts' => [
                            'condition' => 'count_limit',
                            'value' => btutilStripNonNumChars(btflag('WcvYPABR', FLAG_DYNAMIC_PROMPT)),
                            'response' => [
                                'success' => 2,
                                'message' => 'Restricted.',
                                'data' => 'Register to Continue Using GrammarAI.',
                                'modal' => [
                                    'title' => 'Register to Continue Using GrammarAI',
                                    'subTitle' => 'Enjoying GrammarAI so far?',
                                    'desc' => '',
                                    'ctaLabel' => 'CONTINUE',
                                    'redirectUrl' => $ctaModal_logic . $register_url,
                                ],
                            ]
                        ],
                    ],
                ],
            ],
            'dream-photo' => [
                "slug_access" => [
                    'restriction_code' => isPROTECTED,
                    'rules' => [
                        'user_prompts' => [
                            'condition' => 'count_limit',
                            'value' => btutilStripNonNumChars(btflag('WcvYPABR', FLAG_DYNAMIC_PROMPT)),
                            'response' => [
                                'success' => 2,
                                'message' => 'Restricted.',
                                'data' => 'Switch to PRO to Continue Using DreamPhoto',
                                'modal' => [
                                    'title' => 'Register to Continue Using DreamPhoto',
                                    'subTitle' => 'Enjoying the trial version of DreamPhoto so far?',
                                    'desc' => 'Generate hundreds of images!',
                                    'ctaLabel' => 'CONTINUE',
                                    'redirectUrl' => $ctaModal_logic . $register_url,
                                ],
                            ]
                        ],
                    ],
                ],
            ],
            '' => [
                "slug_access" => [
                    'restriction_code' => isPUBLIC,
                    'rules' => [],
                ],
            ]
        ]
    ];

    return $enum;
}

function btenumStatusCode()
{
    $enum = [
        '400' => (object) [
            'code' => '400',
            'message' => 'The output for your prompt is too long for us to process. Please reduce your prompt and try again.',
        ],
        '500' => (object) [
            'code' => '500',
            'message' => 'Please retry your request after a brief wait and contact us if the issue persists.',
        ],
    ];

    return $enum;
}