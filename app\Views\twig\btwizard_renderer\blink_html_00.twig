{% extends 'base_00.twig' %}
{% block styles %}
  {{ parent() }}
  <link rel='stylesheet' href='https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css'>
    <link rel='stylesheet' href='https://cdn.form.io/formiojs/formio.full.min.css'>
      <style>
		.drag-copy[data-type="password"] {
			;
			/*	visibility: hidden;
			display: none;*/
			/*
				*/
		}
	</style>
    {% endblock %}
    {% block pre_scripts %}
      {{ parent() }}
      <script src='https://cdn.form.io/formiojs/formio.full.min.js?ver={{ GLOBAL_DATA.BUILD }}'></script>
      <script type='text/javascript'>
		window.onload = function () {
var force = 1;
var builder_template = localStorage.getItem('btwzrdfrmtmplt');
if (builder_template == null || force == 1) {
builder_template = JSON.stringify({{ VIEW_DATA.btwizard_formio_forms | raw }});
localStorage.setItem('btwzrdfrmtmplt', builder_template);
}

Formio.createForm(document.getElementById('formio'), JSON.parse(builder_template), {}).then(function (form) {
form.on("change", function (e) {
console.log(form.schema);
});
});
};
	</script>
    {% endblock %}
    {% block body_header %}
      {{ parent() }}
    {% endblock %}
    {% block body_modal %}
      {{ parent() }}
    {% endblock %}
    {% block body_content %}
      {{ parent() }}
      <div style="padding: 50px">
        <div id='formio'></div>
      </div>
    {% endblock %}
    {% block body_footer %}
      {{ parent() }}
    {% endblock %}
    {% block post_scripts %}
      {{ parent() }}
      <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.1/jquery.min.js"></script>
      <script>
		// var t = $('.drag-copy[data-type="password"]');
// console.log(t);
// $('.drag-copy[data-i="password"]').remove();

$(document).ready(function () {
console.log("ready!");
// alert('asdf');
$('.btn btn-primary btn-sm btn-block formcomponent drag-copy').filter('[data-type="password"]').remove();
});
	</script>
    {% endblock %}
    