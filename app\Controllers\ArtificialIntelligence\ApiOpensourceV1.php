<?php

namespace App\Controllers\ArtificialIntelligence;

use App\Controllers\ArtificialIntelligence\_BaseArtificialIntelligence as AIBase;
use <PERSON><PERSON><PERSON>\ArtificialIntelligence as BTAI;
use <PERSON><PERSON>ore\ArtificialIntelligence\OpensourceLLM as BTOpensourceLLM;


class ApiOpensourceV1 extends AIBase
{
    //---------------------------------------------------------------------------------------------
    //  variables
    //---------------------------------------------------------------------------------------------

    protected $app;
    protected $appClass;
    protected $sessionName = '';

    //---------------------------------------------------------------------------------------------
    //  public
    //---------------------------------------------------------------------------------------------

    public function __construct()
    {
        parent::__construct();
        header('Access-Control-Allow-Origin: *');

        $this->app = new BTAI();
    }

    public function v1($module = '')
    {
        // btutilDebug($module);
        $res = $this->appResponse;
        $resDeniedAccess = [
            'success' => 0,
            'message' => 'Access denied.',
        ];
        $req = $this->request->getPost();

        $reqSlug = $req['slug'];
        if (!isset($reqSlug) || empty($reqSlug)) {
            return $this->response->setJSON($resDeniedAccess);
        }

        $reqSurfToken = $req['surfToken'];
        if (!isset($reqSurfToken) || empty($reqSurfToken)) {
            return $this->response->setJSON($resDeniedAccess);
        }
        $this->sessionName = $reqSlug . '_' . $reqSurfToken;
        log_message('debug', "Session name: {$this->sessionName}");

        // only accepted slugs
        $allowAccess = false;
        switch ($reqSlug) {
            case 'chatbot':
            case 'convert-to-proper-english':
            case 'start-chatbot':
                $allowAccess = true;
                break;
        }

        if (!$allowAccess) {
            return $this->response->setJSON($resDeniedAccess);
        }
        // -------------------------------------
        // FILTER: authenticateProtectedAccess
        // -------------------------------------

				$tool_count = $this->getToolCount();
				$app_name = 'ChatGPT';
				$app_url = getenv('CHATBOT_URL') ? getenv('CHATBOT_URL') : 'https://app.ai-pro.org/chatbot';
				if($reqSlug == 'convert-to-proper-english') {
					$app_name = 'GrammarAI';
					$app_url = getenv('GRAMMARAI_URL') ? getenv('GRAMMARAI_URL') : 'https://app.ai-pro.org/convert-to-proper-english';
				}
        if (!$this->authenticatePrivateAccess()) {
            switch ($reqSlug) {
								case 'start-chatbot':
								case 'convert-to-proper-english':
                    $resRestrictions = $this->filterRestrictions($reqSlug, $req);
                    if ($resRestrictions) {
                        btflag_set('app', 'basic', ['domain' => '.ai-pro.org']);
                        btflag_set('appurl', $app_url, ['domain' => '.ai-pro.org']);
                        $userEmail = btflag_cookie('aiwp_logged_in', 'unknown|');
                        $region = btflag_cookie('reg', '');
                        $start_url = getenv('START_URL') ? getenv('START_URL') : START_URL;
                        $ctaModal_logic = btflag_cookie('regRedirectWP', '0') === '1' ? WP_URL : $start_url;
                        $register_url = $this->getRegisterUrl();
                        if ($region === 'ar') {
                            $wp_url = WP_URL;
                            $title = 'قم بتغيير اشتراكك إلى PRO لمواصلة الاستخدام ' . $app_name;

                            if(btflag(FLOW, null) === 'basilisk-02') $redirectURL = $wp_url . '/splash';
														else if($userEmail === 'unknown|') $redirectURL = $wp_url . $register_url;
														else $redirectURL = $wp_url . '/pricing';

                            return $this->response->setJSON([
                                'success' => 2,
                                'message' => 'Restricted.',
                                'data' => $title,
                                'modal' => [
                                    'title' => '<div dir="rtl">' . $title . '</div>',
                                    'subTitle' => '<div dir="rtl">هل تستمتع بأستخدام '. $app_name .' ؟</div>',
                                    'desc' => '<div dir="rtl">قم بتغيير اشتراكك إلى PRO للوصول إلى 14 أداة متنوعة ومتقدمة من أدوات الذكاء الاصطناعي</div>',
                                    'ctaLabel' => 'للمتابعة',
                                    'redirectUrl' => $redirectURL
                                ]
                            ]);
                        } else {
                            $title = 'Switch to PRO to Continue Using ' . $app_name;

                            if(btflag(FLOW, null) === 'basilisk-02') $redirectURL = $start_url . '/splash';
                            else if($userEmail === 'unknown|') $redirectURL = $ctaModal_logic . $register_url;
														else $redirectURL = $start_url . '/pricing';

                            return $this->response->setJSON([
                                'success' => 2,
                                'message' => 'Restricted.',
                                'data' => $title,
                                'modal' => [
                                    'title' => $title,
                                    'subTitle' => 'Enjoying the trial version of '. $app_name .' so far?',
                                    'desc' => 'Switch now to PRO and get access to <span>'.$tool_count.'</span> different creativity and productivity AI tools.',
                                    'ctaLabel' => 'CONTINUE',
                                    'redirectUrl' => $redirectURL
                                ]
                            ]);
                        }
                    }
            }
        }

        switch ($module) {
                // case 'text-completion':
                //     $res = $this->textCompletion($req);
                //     break;
            case 'text-completion2':
                $res = $this->chatAsTextCompletion($req);
                break;
            case 'chat-completion':
                $res = $this->chatCompletion($req);
                break;
            default:
        }

        return $this->response->setJSON($res);
    }

    //---------------------------------------------------------------------------------------------
    //  private
    //---------------------------------------------------------------------------------------------

    /**
     * chatAsTextCompletion function
     *
     * @param array $req
     * @return array $res
     */
    private function chatAsTextCompletion($req = [])
    {
        log_message("debug", "chatAsTextCompletion");
        log_message("debug", json_encode($req));

        $res = $this->appResponse;
        if (!isset($req['prompt'])) {
            return $res;
        }

        $userPrompt = $req['prompt']; //should be raw
        $prompt = $userPrompt;
        $trainedPrompt = "";
        $suffixPrompt = "";
        $appParams = [
            'stop' => '\n\n',
        ];

        $slug = $req['slug'];
        $userPrompts = (btsessionHas($this->sessionName)) ? btsessionGet($this->sessionName)->user_prompts : [];
        $conversation = (btsessionHas($this->sessionName)) ? btsessionGet($this->sessionName)->conversation : [];
        $aiResponses = (btsessionGet($this->sessionName) && isset(btsessionGet($this->sessionName)->ai_responses)) ? btsessionGet($this->sessionName)->ai_responses : [];

        $validateAsk = $this->validateAsk($prompt);
        if ($validateAsk) return $validateAsk;
        $userPrompts[] = $prompt;
        btsessionSet($this->sessionName, ["user_prompts" => $userPrompts]);
        $conversation[] = $prompt;
        btsessionSet($this->sessionName, ["conversation" => $conversation]);

        switch ($slug) {
            case 'convert-to-proper-english':

                $trainedPrompt = 'Convert the following text to proper English:"' . trim($userPrompt) . '"';
                $message = [
                    ["role" => "user", "content" => $trainedPrompt],
                ];
                break;
            default:
        }

        $usageTrackingData = [
            'requestData' => $req,
            'prompts' => [
                'user' => $userPrompt,
                'other' => json_encode([
                    'trainedPrompt' => $trainedPrompt,
                    'suffixPrompt' => $suffixPrompt,
                ])
            ],
            'app' => $slug,
        ];

        $this->appClass = new BTOpensourceLLM();
        $this->app->_init($this->appClass);
        $this->app->base64Code = "1J3JfcGUH7BGtSe7";

        if ($suffixPrompt != "") {
            $appParams['suffix'] = trim($suffixPrompt);
        }
        log_message('debug', "usageTrackingData = " . json_encode($usageTrackingData));

        $res_prompt = '';
        try {
            $appParams['url'] = getenv('OPENSOURCE_ENDPOINT') . '/chat/completions';
            $res_prompt = $this->app->chatCompletion($message, $appParams);
            log_message('debug', "res_prompt " . json_encode($res_prompt));
        } catch (\Exception $e) {
            // log_message('debug','ERRORRRRRR' . json_encode($e->getMessage()));
            // return;
            return $this->catchException($e);
        }
        log_message('debug', "res_promptdata " . json_encode($res_prompt['data']));
        log_message('debug', "res_promptdatachoices" . json_encode($res_prompt['data']->choices));


        // btutilDebug($res_prompt['data']);
        $res = [
            'success' => $res_prompt['success'],
            'message' => $res_prompt['message'],
            'data' => $this->filterOutput($res_prompt['data']->choices[0]->message->content, $req['isParagraph']),
            'usageCounter' => json_encode($res_prompt['data']->usage),
        ];
        $usageTrackingData['ai_response'] = $res_prompt['data']->choices[0]->message->content;
        $usageTrackingData['response_usage'] = json_encode($res['usageCounter']);
        $usageTrackingData['promptResponseData'] = $res_prompt;

        $this->trackUsage('success', $usageTrackingData);


        // Update aiResponses and conversation
        $aiResponses[] = trim($res['data']);
        btsessionSet($this->sessionName, ["ai_responses" => $aiResponses]);
        $conversation[] = trim($res['data']);
        btsessionSet($this->sessionName, ["conversation" => $conversation]);
        // unset($res['usageCounter']);
        return $res;
    }

    /**
     * chatCompletion function
     *
     * @param array $req
     * @return array $res
     */
    private function chatCompletion($req = [])
    {
        log_message("debug", "chatCompletion");
        log_message("debug", json_encode($req));

        $res = $this->appResponse;
        if (!isset($req['prompt'])) {
            return $res;
        }

        $userPrompt = $req['prompt']; //should be raw
        $prompt = $userPrompt;
        $trainedPrompt = "";
        $suffixPrompt = "";
        $appParams = [
            'stop' => '\n\n',
        ];

        $slug = $req['slug'];
        // log_message('debug', btsessionHas($this->sessionName));
        $userPrompts = (btsessionHas($this->sessionName)) ? btsessionGet($this->sessionName)->user_prompts : [];
        $conversation = (btsessionHas($this->sessionName)) ? btsessionGet($this->sessionName)->conversation : [];
        $aiResponses = (btsessionGet($this->sessionName) && isset(btsessionGet($this->sessionName)->ai_responses)) ? btsessionGet($this->sessionName)->ai_responses : [];

        $validateAsk = $this->validateAsk($prompt);
        if ($validateAsk) return $validateAsk;
        $userPrompts[] = $prompt;
        btsessionSet($this->sessionName, ["user_prompts" => $userPrompts]);
        $conversation[] = ["role" => "user", "content" => $prompt];
        btsessionSet($this->sessionName, ["conversation" => $conversation]);

        $usageTrackingData = [
            'requestData' => $req,
            'app' => $slug,
        ];

        $this->appClass = new BTOpensourceLLM();
        // $this->appClass = new BTAITectalic();
        $this->app->_init($this->appClass);
        // $this->app->apiKey = getenv('OPENAI_API_KEY');
        $this->app->base64Code = "1J3JfcGUH7BGtSe7";

        if ($suffixPrompt != "") {
            $appParams['suffix'] = trim($suffixPrompt);
        }

        $res_prompt = '';
        try {
            // log_message('debug', json_encode($conversation));
            $appParams['url'] = getenv('OPENSOURCE_ENDPOINT') . '/chat/completions';
            $res_prompt = $this->app->chatCompletion($conversation, $appParams);
        } catch (\Exception $e) {
            $errorCode = $e->getCode();
            $errorMsg = $e->getMessage();
            $status = btenumStatusCode();
            if (str_contains($e->getMessage(), '400 (Bad Request)')) {
                $errorCode = '400';
                $errorMsg = $status[$errorCode]->message;
                btsessionSet($this->sessionName, [
                    "user_prompts" => [],
                ]);
            } elseif (str_contains($e->getMessage(), 'Response body parse failed')) {
                $errorCode = '500';
                $errorMsg = $status[$errorCode]->message;
                btsessionSet($this->sessionName, [
                    "user_prompts" => [],
                ]);
            } elseif (str_contains($e->getMessage(), 'Internal Server Error')) {
                $errorCode = '500';
                $errorMsg = $status[$errorCode]->message;
                btsessionSet($this->sessionName, [
                    "user_prompts" => [],
                ]);
            }
            $res = [
                'success' => 0,
                'message' => "[`$errorCode`]" . $errorMsg,
                'data' => $errorMsg,
            ];
            $usageTrackingData['ai_response'] = $e;
            $usageTrackingData['promptResponseData'] = $res;
            $this->trackUsage('failed', $usageTrackingData);

            \Sentry\captureException($e);
            log_message('error', $errorCode);
            log_message('error', $errorMsg);
            return $this->response->setStatusCode($errorCode, $errorMsg);
        }

        // btutilDebug($res_prompt['data']);
        $res = [
            'success' => $res_prompt['success'],
            'message' => $res_prompt['message'],
            'data' => $this->filterOutput($res_prompt['data']->choices[0]->message->content),
            'usageCounter' => json_encode($res_prompt['data']->usage),
        ];

        // Update aiResponses and conversation
        $aiResponses[] = trim($res['data']);
        btsessionSet($this->sessionName, ["ai_responses" => $aiResponses]);
        $conversation[] = ["role" => "assistant", "content" => trim($res['data'])];
        btsessionSet($this->sessionName, ["conversation" => $conversation]);

        $usageTrackingData['ai_response'] = $res_prompt['data']->choices[0]->message->content;
        $usageTrackingData['response_usage'] = json_encode($res['usageCounter']);
        $usageTrackingData['promptResponseData'] = $res_prompt;
        $usageTrackingData['prompts'] = [
            'user' => $userPrompt,
            'other' => json_encode([
                'trainedPrompt' => $trainedPrompt,
                'suffixPrompt' => $suffixPrompt,
                'conversation' => json_encode($conversation),
            ])
        ];
        $this->trackUsage('success', $usageTrackingData);

        // unset($res['usageCounter']);
        return $res;
    }



    //---------------------------------------------------------------------------------------------
    //  protected
    //---------------------------------------------------------------------------------------------

    protected function filterOutput($string, $isParagraph="")
    {
        if($isParagraph){
            return nl2br(stripslashes($string));
        }
        // log_message('debug', htmlspecialchars_decode($string));
        $filteredString = htmlspecialchars_decode($string) . "\n\n";
        // $filteredString = htmlspecialchars($filteredString,ENT_QUOTES);
        // $filteredString = htmlentities($filteredString);
        $filteredString = str_replace("&amp;lt;pre&amp;gt;", 'OPENPRETAG', $filteredString);
        $filteredString = str_replace("&amp;lt;/pre&amp;gt;", 'CLOSEPRETAG', $filteredString);
        $filteredString = str_replace('<?php', "OPENPRETAG<?php", $filteredString);
        $filteredString = str_replace('?>', "?>CLOSEPRETAG", $filteredString);
        $filteredString = str_replace('<pre>', "OPENPRETAG", $filteredString);
        $filteredString = str_replace('</pre>', "CLOSEPRETAG", $filteredString);
        // log_message('debug', htmlspecialchars_decode($filteredString));
        $filteredString = htmlspecialchars($filteredString, ENT_NOQUOTES | ENT_HTML5, 'UTF-8');
        $filteredString = str_replace('OPENPRETAG', "<pre>", $filteredString);
        $filteredString = str_replace('CLOSEPRETAG', "</pre>", $filteredString);
        return $filteredString;
    }
}
