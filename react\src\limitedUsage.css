@import url("https://fonts.googleapis.com/css2?family=Alegreya+Sans:wght@100;300;400;500;700;800;900&display=swap");

.modal-container {
  display: none;
  position: fixed;
  z-index: 9999;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-container.show {
  display: block;
}

.modal-container .modal-content {
  box-sizing: border-box;
  position: absolute;
  top: 44%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 90%;
  background-color: #0038ff;
  border-radius: 7px;
  width: 500px;
  padding: 20px 30px;
  text-align: center;
  color: #fff;
  min-height: 279px;
  box-shadow: 0 5px 18px -7px #000;
  background-image: url("https://api.ai-pro.org/ext-app/images/bg.png");
  font-family: "Alegreya Sans", sans-serif !important;
}

.limitedUse {
  padding: 50px !important;
}

div.modal-subtitle {
  margin: 0 0 10px 0;
  font-size: 20px;
  font-family: "Alegreya Sans", sans-serif !important;
}

.modal-title {
  font-size: 30px;
  margin: 15px 0 20px;
  padding: 0 20px;
  font-weight: 700;
  font-family: "Alegreya Sans", sans-serif !important;
  color: #fff !important;
}

.modal-desc {
  font-size: 20px;
  margin-bottom: 20px;
  font-family: "Alegreya Sans", sans-serif !important;
}

.modal-desc span {
  font-size: 25px;
  font-weight: 700;
  font-family: "Alegreya Sans", sans-serif !important;
}

.modal-cta {
  display: block;
  margin: 0 auto;
  font-size: 18px;
  background-color: #fff;
  color: #052f5e;
  border: none;
  border-radius: 30px;
  padding: 10px;
  cursor: pointer;
  text-decoration: none;
  width: 50%;
  box-shadow: 0 3px 10px -4px #000;
  font-weight: 700;
  font-family: "Alegreya Sans", sans-serif !important;
  margin-bottom: 30px !important;
}

.max-prompt-limit .modal-desc,
.max-token-ent-limit .modal-desc {
  font-size: 19px;
}

.modal-container .modal-close-container {
  position: absolute;
  right: 20px;
  top: 6px;
}

.modal-container .modal-close-container span {
  font-family: "Alegreya Sans", sans-serif !important;
  font-size: 22px;
}

@media (max-width: 767px) {
  .modal-content {
    max-width: 90%;
    padding: 50px;
  }

  .modal-container:not(.no-subtitle) .modal-title {
    font-size: 26px;
    margin: 15px 0 20px;
    padding: 0;
  }
}

@media (max-width: 500px) {
  .modal-desc,
  div.modal-subtitle {
    font-size: 18px;
  }
}

@media (max-width: 400px) {
  .modal-content {
    max-width: 90%;
    padding: 20px 10px;
  }
}

@media (max-width: 384px) {
  .modal-desc,
  div.modal-subtitle {
    font-size: 17px;
  }

  .modal-desc,
  .modal-title,
  div.modal-subtitle {
    line-height: 1.25;
  }
}

@media (max-width: 369px) {
  .modal-cta {
    width: 70%;
  }
}
