@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

@import "tailwindcss";

@theme {
  --font-inter: "Inter", sans-serif;

  --color-black: #000;
  --color-black-100: #1a1d21;
  --color-black-150: #22252a;
  --color-black-200: #32363d;
  --color-black-300: #4a4a4a;

  --color-gray: #e4e4e7;

  --color-white-300: #d8d8d8;
  --color-white-200: #eeefef;
  --color-white-100: #f1f1f1;
  --color-white-50: #f5f5f5;
  --color-white: #fff;

  /* applied in button */
  --color-primary: #3073d5;
  --color-primary-hover: #3073d5;

  --color-secondary: #fff;
  --color-secondary-hover: #fcfcfc;

  --radius-2lg: 10px;
}

@layer base {
  :root {
    font-family: "Inter", sans-serif;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply dark:bg-black-100 bg-white text-black dark:text-white;
    min-height: 100dvh;
    height: 100dvh;
  }
}

.bg-gradient-blue {
  background: linear-gradient(90deg, #3e53b8 0%, #2393f1 100%);
}

.bg-gradient-blue:hover {
  background: linear-gradient(90deg, #3e53b8 21.55%, #2393f1 146.32%);
  box-shadow: 0 4px 3px 0 rgba(0, 0, 0, 0.11);
}

.bg-gradient-black {
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 50%,
    rgba(0, 0, 0, 0.88) 100%
  );
}

@layer components {
  /* Custom component styles can be added here */

  .heading-1 {
    font-size: 32px;
    font-weight: 600;
  }

  .heading-2 {
    font-size: 24px;
  }

  .label-1 {
    @apply text-black-100 dark:text-white-100;
    font-size: 14px;
    font-weight: 600;
  }

  .label-2 {
    @apply text-black-300 dark:text-white-300;
    font-size: 12px;
  }

  .sub-title {
    font-size: 16px;
  }

  .caption {
    font-size: 15px;
  }
}

.scroll-1 {
  scrollbar-color: #eeefef transparent;
  scrollbar-width: thin;
}

@media (prefers-color-scheme: dark) {
  .scroll-1 {
    scrollbar-color: #32363d transparent;
  }
}

.multiline-ellipsis {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  /* for Internet Explorer, Edge */
  scrollbar-width: none;
  /* for Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
  /* for Chrome, Safari, and Opera */
}

@keyframes swal-show {
  0% {
    transform: scale(0.7);
    opacity: 0;
  }

  45% {
    transform: scale(1.05);
    opacity: 1;
  }

  80% {
    transform: scale(0.95);
  }

  100% {
    transform: scale(1);
  }
}

.swal-show {
  animation: swal-show 0.4s ease-out;
}

@keyframes linear-left-right {
  0% {
    left: -10%;
  }

  100% {
    left: 85%;
  }
}

.linear-loader {
  width: 30px;
  height: 100%;
  background: linear-gradient(
    270deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.66) 41.83%,
    rgba(255, 255, 255, 0) 75%
  );
  mix-blend-mode: screen;
  animation: linear-left-right 2s linear infinite;
}

/* width */
.custom-scroll-bar::-webkit-scrollbar {
  width: 2px;
}

/* Track */
.custom-scroll-bar::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* Handle */
.custom-scroll-bar::-webkit-scrollbar-thumb {
  background: #888;
}

/* Handle on hover */
.custom-scroll-bar::-webkit-scrollbar-thumb:hover {
  background: #555;
}

@media (prefers-color-scheme: dark) {
  .custom-scroll-bar::-webkit-scrollbar-track {
    background: #32363d;
  }

  .custom-scroll-bar::-webkit-scrollbar-thumb {
    background: #888;
  }

  .custom-scroll-bar::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}

@media (min-width: 640px) {
  .sm\:w-\[60\%\] {
    width: 60%;
  }

  .sm\:w-\[95\%\] {
    width: 95%;
  }

  .sm\:max-w-\[95\%\] {
    max-width: 95%;
  }
}

@media (min-width: 768px) {
  .md\:w-\[68\%\] {
    width: 68%;
  }

  .md\:w-full {
    width: 100%;
  }

  .md\:min-w-\[30\%\] {
    min-width: 30%;
  }

  .md\:min-w-\[710px\] {
    min-width: 710px;
  }

  .md\:max-w-\[62\%\] {
    max-width: 62%;
  }

  .md\:max-w-\[68\%\] {
    max-width: 68%;
  }
}

@media (min-width: 1024px) {
  .lg\:w-\[88\%\] {
    width: 88%;
  }

  .lg\:max-w-\[50\%\] {
    max-width: 50%;
  }

  .lg\:max-w-\[88\%\] {
    max-width: 88%;
  }
}

@media (min-width: 1280px) {
  .xl\:h-\[70\%\] {
    height: 70%;
  }

  .xl\:max-w-\[36\%\] {
    max-width: 36%;
  }
}

@media (min-width: 1200px) {
  .min-w-1200\:w-\[70\%\] {
    width: 70%;
  }

  .min-w-1200\:max-w-\[70\%\] {
    max-width: 70%;
  }
}

@media (min-width: 1440px) {
  .min-w-1440\:w-\[60\%\] {
    width: 60%;
  }

  .min-w-1440\:w-\[68\%\] {
    width: 68%;
  }

  .min-w-1440\:max-w-\[60\%\] {
    max-width: 60%;
  }

  .min-w-1440\:max-w-\[68\%\] {
    max-width: 68%;
  }
}

@media (min-width: 1920px) {
  .min-w-1920\:w-\[42\%\] {
    width: 42%;
  }
}

@media (min-width: 2560px) {
  .min-w-2560\:h-\[50\%\] {
    height: 50%;
  }

  .min-w-2560\:h-\[71\%\] {
    height: 71%;
  }

  .min-w-2560\:min-w-\[26\%\] {
    min-width: 26%;
  }

  .min-w-2560\:min-w-\[31\%\] {
    min-width: 31%;
  }

  .min-w-2560\:max-w-\[28\%\] {
    max-width: 28%;
  }

  .min-w-2560\:max-w-\[31\%\] {
    max-width: 31%;
  }
}

@media (max-height: 720px) {
  .max-h-720\:h-\[94\%\] {
    height: 94%;
  }
}

@media (max-width: 767px) {
  .max-w-767\:h-\[71\%\] {
    height: 71%;
  }
}

@media (min-width: 768px) and (max-width: 1280px) {
  .max-w-768-1280\:h-\[90\%\] {
    height: 90%;
  }
}

@media (min-width: 1870px) and (max-width: 1875px) {
  .max-w-1870-1875\:w-\[40\%\] {
    width: 40%;
  }
}

@media (min-width: 1910px) and (max-width: 1915px) {
  .max-w-1910-1915\:w-\[45\%\] {
    width: 45%;
  }
}

@media (min-width: 1150px) and (max-width: 1155px) {
  .max-w-1150-1155\:w-\[68\%\] {
    width: 68%;
  }
}
