<?php

namespace App\Controllers\ArtificialIntelligence;

use App\Controllers\ArtificialIntelligence\_BaseArtificialIntelligence as AIBase;



class App extends AIBase
{
    protected $isProtected = ISPROTECTED_APP;

    private $theme = 'avalanche';
    private $themePageVersion = '01';
    private $themeSlug = null;

    public function __construct()
    {
        parent::__construct();
    }

    public function index($slug = '')
    {
        $this->themeSlug = $slug;
        $this->initSession();

        // -------------------------------------
        // FILTER: authenticatePrivateAccess
        // -------------------------------------
        switch ($this->themeSlug) {
            // commented out; for this will be handledin authV2 JS
            // case "convert-to-proper-english":
            case "convert-to-proper-english":
                setcookie("app", "pro", time() + (86400 * 30), "/", ".ai-pro.org");
                $this->authenticatePrivateAccess();
                break;
            case "create-art":
            case "dream-photo":
            case "text-to-image":
                setcookie("app", "basic", time() + (86400 * 30), "/", ".ai-pro.org");
                break;
                // if (!$this->authenticatePrivateAccess()) {
                //     return redirect()->to(AUTH_FAILED_REDIRECTION_URL);
                // }
                // break;
        }

        $this->theme = btflag('theme', $this->theme);

        $this->theme_data();
        $this->theme_pageVersion();
        $this->theme_avalanche();
    }

    // public function testImage()
    // {
    //     $apiV2 = new ApiV2;
    //     // btutilDebug($apiV2->v2('test'));
    //     $res = $apiV2->textToImage();
    //     // print_r($res['data']);
    //     foreach ($res['data']->images as $key => $value) {
    //         // print_r($value);
    //         echo '<img src="data:image/png;base64, ' . $value .'" />';
    //     }
    // }

    //---------------------------------------------------------------------------------------------
    //  protected
    //---------------------------------------------------------------------------------------------

    protected function initSession()
    {
        $date_now = date('Y-m-d H:i:s', time());
        $this->sessionId = btutilGenerateHashId([$date_now, '1J3JfcGUH7BGtSe7']);
        $sessionName = $this->themeSlug . '_' . $this->sessionId;
        if (!$this->session->has($sessionName)) {
            $this->session->set($sessionName, []);
        }


        switch ($this->themeSlug) {
            case "convert-to-proper-english":
            case "create_art":
                $this->session->push($sessionName, [
                    "slug_access" => [
                        'restriction_code' => isPRIVATE,
                        'rules' => [],
                    ],
                ]);
                break;
            case "text-to-image":
                $this->session->push($sessionName, [
                    "slug_access" => [
                        'restriction_code' => isPRIVATE,
                        'rules' => [],
                    ],
                ]);
                break;
            default:
                $this->session->push($sessionName, [
                    "slug_access" => [
                        'restriction_code' => isPUBLIC,
                        'rules' => [],
                    ],
                ]);
        }

        $this->session->push($sessionName, [
            'user_prompts' => [],
            'ai_responses' => [],
            'conversation' => [],
            'surfToken' => $this->sessionId,
        ]);
    }


    //---------------------------------------------------------------------------------------------
    //  private
    //---------------------------------------------------------------------------------------------

    private function theme_avalanche()
    {
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData
        ];
        // btutilDebug($viewData);

        //TODO: must be done generic
        switch($this->themeSlug) {
            case 'convert-to-proper-english':
                $this->bttwig->view('/ai_app.twig', $viewData);
            break;
            case 'create-art':
                $this->bttwig->view('/ai_app/avalanche/page_aiHome_outputImage_B01.twig', $viewData);
            break;
            case 'dream-photo':
                $this->bttwig->view('/ai_app/avalanche/page_aiHome_outputImage_B01_V2.twig', $viewData);
            break;
            case 'text-to-image':
                $this->bttwig->view('/ai_app/avalanche/page_aiHome_outputImage_B02.twig', $viewData);
            break;
            default:
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
            // $this->bttwig->view('/ai_app/avalanche/page_aiHome_outputText_B01.twig', $viewData);
        }
    }

    private function theme_data()
    {
        $btutil_auth_url =  getenv('BTUTIL_ASSET_URL');
        $btForm = [];
        $metaTitle = '';
        $metaDescription = '';
        $mixpanel = false;
        $favicon = '';
        $favicons = '';

        switch($this->themeSlug) {
            case 'convert-to-proper-english':
                $btutil_auth_url = getenv('BTUTIL_ASSET_URL_with_limited_usage');
                $btForm = [
                    'page_title' => 'Grammar AI',
                    'submit_label' => 'Generate Proper English',
                    'textfield_label' => 'Prompt',
                ];
                $metaTitle = 'Grammar AI | AI-Pro.org';
                $favicon = '/favicon_grammarai.png';
                $metaDescription = 'Convert texts to proper English without slang or grammatical errors. Translate languages to proper English language in seconds.'; // Set specific meta description
                $mixpanel = true;
                $favicons = '/favicon_grammarai';
                break;
            case 'create-art':
                $btForm = [
                    'page_title' => 'DreamPhoto',
                    'submit_label' => 'Generate Image',
                    'inputPositivePrompt_label' => ' Detailed descriptions of the image you want to create',
                    'inputNegativePrompt_label' => ' Detailed descriptions of the things you do not want to see in your image',
                ];
                $metaTitle = 'DreamPhoto | AI-Pro.org';
                $favicon = '/favicon_dreamphoto.png';
                $metaDescription = 'Create amazing digital artwork with the latest AI Image Generation technology.';
                break;
            case 'translate-from-english':
                $btForm = [
                    'page_title' => 'Translate from English',
                    'submit_label' => 'Generate',
                    'textfield_label' => 'Prompt',
                ];
                $metaTitle = 'GrammarAI | AI-Pro.org';
                $favicon = '/favicon_grammarai.png';
                $metaDescription = 'Convert texts to proper English without slang or grammatical errors. Translate languages to proper English language in seconds.';
                $favicons = '/favicon_grammarai';
                break;
            case 'dream-photo':
                $btutil_auth_url = getenv('BTUTIL_ASSET_URL_with_limited_usage');
                $btForm = [
                    'dp_models' => json_decode(getenv('SD_MODELS'),true),
                    'upgrade_url' => getenv('UPGRADE_URL')
                ];
                $metaTitle = 'DreamPhoto | AI-Pro.org';
                $favicon= '/favicon_dreamphoto.png';
                $metaDescription = 'Create amazing digital artwork from text and phrases using the latest AI Image Generation technology.';
                break;
            case 'text-to-image':
                $metaTitle = 'Dream Photo | AI-Pro.org';
                $favicon= '/favicon_dreamphoto.png';
                $metaDescription = 'Create amazing digital artwork from text and phrases using the latest AI Image Generation technology.';
				$mixpanel = true;
                break;
            default:
                // throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
                $btForm = [
                    'page_title' => 'Custom',
                ];
        }

        $flagShowUsage = btflag_get('showUsage', 0);
        $this->themePageData = [
            'page_title' => $metaTitle,
            'meta_data' => [
                'description' => $metaDescription,
                // 'robots' => 'noindex, follow', //uncomment and change as needed
            ],
            'favicon' => $favicon,
            'favicons' => $favicons,
            'app_dashboard_url' => getenv('DASHBOARD_REDIRECTION_URL'),
            'btutil_auth_url' => $btutil_auth_url,
            'slug' => $this->themeSlug,
            'btform' => $btForm,
            'flagShowUsage' => $flagShowUsage,
            'surfToken' => ['name' => 'surfToken', 'hash' => $this->sessionId],
            'OPENSOURCE_ENDPOINT' => getenv('OPENSOURCE_ENDPOINT'),
            'API_URL' => getenv('API_URL'),
            'include_mixpanel' => $mixpanel ? [
                'debug' => CONFIG_MIXPANEL_DEBUG,
                'mixpanel_name' => 'app_chatbot',
                'keyword' => btflag('keyword', ''),
                'emailid' => btflag('emailid', ''),
                'adid' => btflag('adid', ''),
                'ppg' => btflag('ppg', ''),
                'pmt' => btflag('pmt', ''),
                'email' => btflag_cookie('user_email', ''),
                'user_plan' => btflag_cookie('user_plan', ''),
                'howdoiplantouse' => btflag_cookie('howdoiplantouse', ''),
                'remakemedloption' => btflag_cookie('remakemedloption', '')
            ] : false,
            'init_mixpanel' => false,
            'include_gtag_AW' => true,
            'include_gtag_GA4' => true,
            'subscription_type' => isset($this->userDetails['subscription_type']) ? $this->userDetails['subscription_type'] : '',
        ];
    }

    private function theme_pageVersion()
    {
        $this->themePageVersion = '01';
        // $pageVer = btflag_get('v',  $pageVer);

        return $this->themePageVersion;
    }

}
