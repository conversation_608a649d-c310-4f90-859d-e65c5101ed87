{"name": "codebay/btapp", "description": "BTApp", "type": "project", "minimum-stability": "dev", "prefer-stable": true, "repositories": [{"url": "https://gitlab.baytech.ph/baytech/btcore.git", "type": "vcs", "require-all": false}, {"url": "https://gitlab.baytech.ph/baytech/btcomponents.git", "type": "vcs", "require-all": false}], "require": {"btapp/btcore": "dev-master", "codebay/btcomponents": "dev-master", "php": "^8.0", "codeigniter4/framework": "4.3.8", "mobiledetect/mobiledetectlib": "^2.8.41", "guzzlehttp/guzzle": "^6.0", "sentry/sdk": "^3.3"}, "require-dev": {"fakerphp/faker": "^1.9", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": "^9.1"}, "suggest": {"ext-fileinfo": "Improves mime type detection for files"}, "autoload": {"psr-4": {"App\\": "app", "Config\\": "app/Config"}, "exclude-from-classmap": ["**/Database/Migrations/**"]}, "scripts": {"localBuild": ["rm composer.lock || true", "composer clear-cache", "composer install"], "localBuildAll": ["rm composer.lock || true", "composer clear-cache", "composer install", "npm cache clear --force", "npm install", "npm run prod", "php spark migrate --all"], "debugDbMigration": ["php spark migrate:rollback", "php spark migrate --all"], "importChatBotV2": ["cd vendor/codebay/btcomponents/CHATBOT/client_v2 && npm run buildProd", "sh scripts/btcomponents/import-chatbot_client_v2.sh"], "localServe0": "php spark serve --host localhost --port 9000", "localServe1": "php spark serve --host localhost --port 9001", "localServe2": "php spark serve --host localhost --port 9002", "radBuild": ["sh _srv/radBuild/01-build_init.sh"], "zLIVEBuild": ["sh _srv/zLIVEBuild/01-build_init.sh"], "1DEVTBuild": ["sh _srv/1DEVTBuild/01-build_init.sh", "sh _srv/1DEVTBuild/06-import_scripts.sh"], "aSTAGBuild": ["sh _srv/aSTAGBuild/01-build_init.sh", "sh _srv/aSTAGBuild/06-import_scripts.sh"]}, "config": {"process-timeout": 0, "allow-plugins": {"php-http/discovery": false}}}