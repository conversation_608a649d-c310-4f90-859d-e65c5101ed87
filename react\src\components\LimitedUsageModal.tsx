import { setCookie, getC<PERSON><PERSON> } from "../utils/cookie";
import { useEffect, useState } from "react";

export default function LimitedUsageModal({
  desc,
  show,
  setShowPricing,
  appName,
  app = "",
}: {
  desc: string;
  show: boolean;
  setShowPricing: (value: boolean) => void;
  appName: string;
  app?: string;
}) {
  const startUrl = import.meta.env.VITE_START_URL || "https://start.ai-pro.org";
  const [hasEmail, setEmail] = useState<string | undefined>("");

  useEffect(() => {
    setEmail(getCookie("user_email"));
  }, []);
  const onClick = () => {
    setCookie("app", app, { domain: ".ai-pro.org" });
    setCookie("appName", appName, { domain: ".ai-pro.org" });

    const plan = window.subscription_type?.toString().toLowerCase();

    if (plan === "basic") {
      setCookie("kt8typtb", "arcana", { domain: ".ai-pro.org" });
      setCookie("ppg", "59", { domain: ".ai-pro.org" });
      window.location.href = `${startUrl}/upgrade?app=${app}&ppg=59&utpm=`;
      setTimeout(() => {
        window.location.href = `${startUrl}/upgrade?app=${app}&ppg=59&appName=${encodeURI(appName)}&utpm=`;
      }, 300);
      return;
    }
    if (hasEmail) {
      setCookie("kt8typtb", "arcana", { domain: ".ai-pro.org" });
      setCookie("ppg", "41", { domain: ".ai-pro.org" });
      setTimeout(() => {
        window.location.href = `${startUrl}/pricing?app=${app}&ppg=41&appName=${encodeURI(appName)}`;
      }, 300);
      return;
    }
    setShowPricing(true);
  };

  return (
    <div className={`modal-container ${show ? "show" : "hidden"}`}>
      <div className="modal-content">
        <div className="modal-subtitle">
          Enjoying the trial version of {appName} so far?
        </div>
        <h2 className="modal-title">
          Switch to PRO to Continue Using {appName}
        </h2>
        <p
          className="modal-desc"
          dangerouslySetInnerHTML={{ __html: desc }}
        ></p>
        <button className="modal-cta" onClick={onClick}>
          CONTINUE
        </button>
      </div>
    </div>
  );
}
