<?php

namespace App\Controllers\ArtificialIntelligence;

use App\Controllers\ArtificialIntelligence\_BaseArtificialIntelligence as AIBase;
use <PERSON>Core\ArtificialIntelligence as BTAI;
use <PERSON><PERSON>ore\ArtificialIntelligence\Tectalic<PERSON>penAi as BTAITectalic;

/**
 *  OpenAI API --> https://platform.openai.com/docs/introduction/overview
 */
class ApiOpenaiV1 extends AIBase
{
    //---------------------------------------------------------------------------------------------
    //  variables
    //---------------------------------------------------------------------------------------------

    protected $app;
    protected $appClass;
    protected $sessionName = '';

    //---------------------------------------------------------------------------------------------
    //  public
    //---------------------------------------------------------------------------------------------

    public function __construct()
    {
        parent::__construct();
        header('Access-Control-Allow-Origin: *');

        $this->app = new BTAI();
				if(!$this->validateOrigin()) {
					header("HTTP/1.1 401 Unauthorized");
					exit;
				}
    }

    public function v1($module = '')
    {
        // btutilDebug($module);
        $res = $this->appResponse;
        $resDeniedAccess = [
            'success' => 0,
            'message' => 'Access denied.',
        ];
        $req = $this->request->getPost();

        $reqSlug = $req['slug'];
        if (!isset($reqSlug) || empty($reqSlug) ){
            return $this->response->setJSON($resDeniedAccess);
        }

        $reqSurfToken = $req['surfToken'];
        if (!isset($reqSurfToken) || empty($reqSurfToken) ){
            return $this->response->setJSON($resDeniedAccess);
        }
        $this->sessionName = $reqSlug . '_' . $reqSurfToken;
        log_message('debug', "Session name: {$this->sessionName}");

        // only accepted slugs
        $allowAccess = false;
        switch ($reqSlug) {
            case 'chatbot':
            case 'convert-to-proper-english':
            case 'start-chatbot':
                $allowAccess = true;
                break;
        }

        if (!$allowAccess) {
            return $this->response->setJSON($resDeniedAccess);
        }
        // -------------------------------------
        // FILTER: authenticateProtectedAccess
        // -------------------------------------

				$privateAccess = $this->authenticatePrivateAccess();
				$start_url = getenv('START_URL') ? getenv('START_URL') : START_URL;
				$tool_count = $this->getToolCount();
				$app_plan = 'basic';
				$app_name = 'ChatGPT';
				$app_url = getenv('CHATBOT_URL') ? getenv('CHATBOT_URL') : 'https://app.ai-pro.org/chatbot';
				if($reqSlug == "convert-to-proper-english") {
					$app_plan = 'pro';
					$app_name = 'GrammarAI';
					$app_url = getenv('GRAMMARAI_URL') ? getenv('GRAMMARAI_URL') : 'https://app.ai-pro.org/convert-to-proper-english';
				}
        if(!$privateAccess) {
            switch ($reqSlug) {
                case 'start-chatbot':
								case 'convert-to-proper-english':
                    $resRestrictions = $this->filterRestrictions($reqSlug, $req);
                    if ($resRestrictions && $app_url) {
                        btflag_set('app', $app_plan, ['domain'=>'.ai-pro.org']);
                        btflag_set('appurl', $app_url, ['domain'=>'.ai-pro.org']);
                        $userEmail = btflag_cookie('aiwp_logged_in', 'unknown|');
                        $region = btflag_cookie('reg', '');
                        $ctaModal_logic = btflag_cookie('regRedirectWP', '0') === '1' ? WP_URL : $start_url;
												$register_url = $this->getRegisterUrl();

                        if($region === 'ar'){
                            $wp_url = WP_URL;
                            $title = 'قم بتغيير اشتراكك إلى PRO لمواصلة الاستخدام ' . $app_name;

                            if(btflag(FLOW, null) === 'basilisk-02') $redirectURL = $wp_url . '/splash';
														else if($userEmail === 'unknown|') $redirectURL = $wp_url . $register_url;
														else $redirectURL = $wp_url . '/pricing';

                            return $this->response->setJSON([
                                'success' => 2,
                                'message' => 'Restricted.',
                                'data' => $title,
                                'modal' => [
                                    'title' => '<div dir="rtl">' . $title . '</div>',
                                    'subTitle' => '<div dir="rtl">هل تستمتع بأستخدام '. $app_name .' ؟</div>',
                                    'desc' => '<div dir="rtl">قم بتغيير اشتراكك إلى PRO للوصول إلى 14 أداة متنوعة ومتقدمة من أدوات الذكاء الاصطناعي</div>',
                                    'ctaLabel' => 'للمتابعة',
                                    'redirectUrl' => $redirectURL
                                ]
                            ]);
                        } else {
                            $title = 'Switch to PRO to Continue Using '. $app_name;

                            if(btflag(FLOW, null) === 'basilisk-02') $redirectURL = $start_url . '/splash';
                            else if($userEmail === 'unknown|') $redirectURL = $ctaModal_logic . $register_url;
														else $redirectURL = $start_url . '/pricing';

                            return $this->response->setJSON([
                                'success' => 2,
                                'message' => 'Restricted.',
                                'data' => $title,
                                'modal' => [
                                    'title' => $title,
                                    'subTitle' => 'Enjoying the trial version of '. $app_name .' so far?',
                                    'desc' => 'Switch now to PRO and get access to <span>'.$tool_count.'</span> different creativity and productivity AI tools.',
                                    'ctaLabel' => 'CONTINUE',
                                    'redirectUrl' => $redirectURL
                                ]
                            ]);
                        }

                    }
            }
        } else {
					$app = '';
					switch ($module) {
						case 'chat-completion':
							$app = 'chatbot';
							break;
						case 'text-completion2':
							$app = 'grammarai';
							$app_plan = 'pro';
							btflag_set('app', $app_plan, ['domain'=>'.ai-pro.org']);
							if(strtolower($this->userDetails['subscription_type']) === 'basic') {
								$title = 'Switch to PRO to Continue Using GrammarAI';
								$redirectURL = $start_url . "/upgrade";
								return $this->response->setJSON([
										'success' => 2,
										'message' => 'Restricted.',
										'data' => $title,
										'modal' => [
												'title' => $title,
												'subTitle' => '',
												'desc' => 'Switch now to PRO and get access to <span>'.$tool_count.'</span> different creativity and productivity AI tools.',
												'ctaLabel' => 'CONTINUE',
												'redirectUrl' => $redirectURL
										]
								]);
								break;
							}
					}
					if($app) {
						$usage = $this->getChatUsage('chatbot');
						if($usage && $usage->status !== 'valid') {
							return $this->response->setJSON($usage);
						}
					}
				}

        switch ($module) {
            case 'text-completion':
                $res = $this->textCompletion($req);
                break;
            case 'text-completion2':
                $res = $this->chatAsTextCompletion($req);
                break;
            case 'chat-completion':
                $res = $this->chatCompletion($req);
                break;
            default:
        }

        return $this->response->setJSON($res);
    }

    //---------------------------------------------------------------------------------------------
    //  private
    //---------------------------------------------------------------------------------------------

		private function validateOrigin() {
			$origin = $this->getOrigin();
			$base_url = base_url();

			if(substr($origin, -1) == '/') {
				$origin = substr($origin, 0, -1);
			}

			if(substr($base_url, -1) == '/') {
				$base_url = substr($base_url, 0, -1);
			}

			if($base_url === $origin) return true;

			return false;
		}

		private function getChatUsage($app = '') {
			if(!$app) return;
			// chat token usage
			$url = getenv('API_URL') . '/e/get-chat-usage';
			$ch_data = [
					'aiwp_logged_in' => btflag_cookie('aiwp_logged_in', ''),
					'app' => $app
			];
			$ch = curl_init($url);
			curl_setopt($ch, CURLOPT_POST, true);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($ch_data));
			curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type:text/json'));
			$response = curl_exec($ch);
			curl_close($ch);

			return $response ? json_decode($response) : [];
		}

    private function getOrigin() {
			$origin = "";
			if (array_key_exists('HTTP_ORIGIN', $_SERVER)) {
					$origin = $_SERVER['HTTP_ORIGIN'];
			}
			else if (array_key_exists('HTTP_REFERER', $_SERVER)) {
					$origin = $_SERVER['HTTP_REFERER'];
			} else {
					$origin = $_SERVER['REMOTE_ADDR'];
			}

			return $origin;
    }

    /**
     * chatAsTextCompletion function
     *
     * @see https://platform.openai.com/docs/guides/chat/chat-vs-completions
     *
     * @param array $req
     * @return void
     */
    private function chatAsTextCompletion($req = [])
    {
        log_message("debug", "chatAsTextCompletion");
        log_message("debug", json_encode($req));

        $res = $this->appResponse;
        if (!isset($req['prompt'])) {
            return $res;
        }

        $date_now = date('F d, Y', time());
        $userPrompt = $req['prompt']; //should be raw
        $prompt = $userPrompt;
        $trainedPrompt = "";
        $suffixPrompt = "";
        $appParams = [
            'stop' => '\n\n',
        ];

        $slug = $req['slug'];

        // log_message('debug', btsessionHas($this->sessionName));
        $userPrompts = (btsessionHas($this->sessionName)) ? btsessionGet($this->sessionName)->user_prompts : [];
        $conversation = (btsessionHas($this->sessionName)) ? btsessionGet($this->sessionName)->conversation : [];
        $aiResponses = (btsessionGet($this->sessionName) && isset(btsessionGet($this->sessionName)->ai_responses)) ? btsessionGet($this->sessionName)->ai_responses : [];

        $validateAsk = $this->validateAsk($prompt);
        if($validateAsk) return $validateAsk;
        $userPrompts[] = $prompt;
        btsessionSet($this->sessionName, ["user_prompts" => $userPrompts]);
        $conversation[] = $prompt;
        btsessionSet($this->sessionName, ["conversation" => $conversation]);

        switch ($slug) {
            case 'convert-to-proper-english':
                // $trainedPrompt = 'Convert "' . trim($userPrompt) . '" to proper English.';
                
                $trainedPrompt = 'Convert the following text to proper English without adding any words: "' . trim($userPrompt) . '"';
                $message = [
                    ["role" => "user", "content" => $trainedPrompt],
                ];
                break;
            default:
        }

        $usageTrackingData = [
            'requestData' => $req,
            'prompts' => [
                'user' => $userPrompt,
                'other' => json_encode([
                    'trainedPrompt' => $trainedPrompt,
                    'suffixPrompt' => $suffixPrompt,
                ])
            ],
            'app' => $slug,
        ];

        $this->appClass = new BTAITectalic();
        $this->app->_init($this->appClass);
        $this->app->apiKey = getenv('OPENAI_API_KEY');
        $this->app->base64Code = "1J3JfcGUH7BGtSe7";

        if ($suffixPrompt != "") {
            $appParams['suffix'] = trim($suffixPrompt);
        }

        $max_token =  getenv('MAX_TOKEN_grammarAI');
        $appParams['max_tokens'] =  $max_token ? (int) $max_token : 1024;
        $appParams['model'] = getenv('DEFAULT_MODEL') ?? 'gpt-4o-mini';

        $res_prompt = '';
        try {
            // log_message('debug', json_encode($message));
            $res_prompt = $this->app->chatCompletion($message, $appParams);
            // log_message('debug', json_encode($res_prompt));
        } catch (\Exception$e) {
            return $this->catchException($e);
        }

        // btutilDebug($res_prompt['data']);
        $res = [
            'success' => $res_prompt['success'],
            'message' => $res_prompt['message'],
            'data' => $this->filterOutput($res_prompt['data']->choices[0]->message->content, $req['isParagraph']),
            'usageCounter' => json_encode($res_prompt['data']->usage),
        ];
        $usageTrackingData['ai_response'] = $res_prompt['data']->choices[0]->message->content;
        $usageTrackingData['response_usage'] = json_encode($res['usageCounter']);
        $usageTrackingData['promptResponseData'] = $res_prompt;
        $this->trackUsage('success', $usageTrackingData);

        // chat token usage
        $url = getenv('API_URL') . '/e/set-chat-usage';
        $ch_data = [
            'aiwp_logged_in' => btflag_cookie('aiwp_logged_in', ''),
            'app' => 'grammarai',
            'prompt_token' => $res_prompt['data']->usage->prompt_tokens,
            'total_token' => $res_prompt['data']->usage->total_tokens,
            'model' => $appParams['model'],
        ];
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($ch_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type:text/plain'));
        $response = curl_exec($ch);
        curl_close($ch);

        // Update aiResponses and conversation
        $aiResponses[] = trim($res['data']);
        btsessionSet($this->sessionName, ["ai_responses" => $aiResponses]);
        $conversation[] = trim($res['data']);
        btsessionSet($this->sessionName, ["conversation" => $conversation]);
        // unset($res['usageCounter']);
        return $res;
    }


    /**
     * chatCompletion function
     *
     * @param array $req
     * @return void
     */
    private function chatCompletion($req = [])
    {
        log_message("debug", "chatCompletion");
        log_message("debug", json_encode($req));

        $res = $this->appResponse;
        if (!isset($req['prompt'])) {
            return $res;
        }

        $date_now = date('F d, Y', time());
        $userPrompt = $req['prompt']; //should be raw
        $prompt = $userPrompt;
        $trainedPrompt = "";
        $suffixPrompt = "";
        $appParams = [
            'stop' => '\n\n',
        ];

        $slug = $req['slug'];
        // log_message('debug', btsessionHas($this->sessionName));
        $userPrompts = (btsessionHas($this->sessionName)) ? btsessionGet($this->sessionName)->user_prompts : [];
        $conversation = (btsessionHas($this->sessionName)) ? btsessionGet($this->sessionName)->conversation : [];
        $aiResponses = (btsessionGet($this->sessionName) && isset(btsessionGet($this->sessionName)->ai_responses)) ? btsessionGet($this->sessionName)->ai_responses : [];

        $validateAsk = $this->validateAsk($prompt);
        if($validateAsk) return $validateAsk;
        $userPrompts[] = $prompt;
        btsessionSet($this->sessionName, ["user_prompts" => $userPrompts]);
        $conversation[] = ["role" => "user", "content" => $prompt];
        btsessionSet($this->sessionName, ["conversation" => $conversation]);

        switch ($slug) {
            case 'chatbot':
            case 'start-chatbot':
                // $prompt = implode("\n", btsessionGet($this->sessionName)->conversation);
                // $prompt = "Answer \"{$prompt}\" as politely and concisely as possible.";


                $message = [
                    ["role" => "system", "content" => "You are ChatGPT, a large language model trained by OpenAI and improved by AI-Pro. Answer as concisely as possible.\nCurrent date: {$date_now}\nKnowledge cutoff: 2021-09-01"]
                ];
                $message = array_merge($message, $conversation);
                log_message("debug", json_encode($message));

                $appParams['model'] = getenv('DEFAULT_MODEL') ?? 'gpt-4o-mini';
                break;
            default:
        }

        $usageTrackingData = [
            'requestData' => $req,
            'app' => $slug,
        ];

        $this->appClass = new BTAITectalic();
        $this->app->_init($this->appClass);
        $this->app->apiKey = getenv('OPENAI_API_KEY');
        $this->app->base64Code = "1J3JfcGUH7BGtSe7";

        if ($suffixPrompt != "") {
            $appParams['suffix'] = trim($suffixPrompt);
        }

        $max_token =  getenv('MAX_TOKEN');
        $appParams['max_tokens'] =  $max_token ? (int) $max_token : 1024;

        $res_prompt = '';
        try {
            // log_message('debug', json_encode($message));
            $res_prompt = $this->app->chatCompletion($message, $appParams);
            // log_message('debug', json_encode($res_prompt));
        } catch (\Exception$e) {
            $errorCode = $e->getCode();
            $errorMsg = $e->getMessage();
            $status = btenumStatusCode();
            if (str_contains($e->getMessage(), '400 (Bad Request)')) {
                $errorCode = '400';
                $errorMsg = $status[$errorCode]->message;
                btsessionSet($this->sessionName, [
                    "user_prompts" => [],
                ]);
            } elseif (str_contains($e->getMessage(), 'Response body parse failed')) {
                $errorCode = '500';
                $errorMsg = $status[$errorCode]->message;
                btsessionSet($this->sessionName, [
                    "user_prompts" => [],
                ]);
            } elseif (str_contains($e->getMessage(), 'Internal Server Error')) {
                $errorCode = '500';
                $errorMsg = $status[$errorCode]->message;
                btsessionSet($this->sessionName, [
                    "user_prompts" => [],
                ]);
            }
            $res = [
                'success' => 0,
                'message' => "[`$errorCode`]" . $errorMsg,
                'data' => $errorMsg,
            ];
            $usageTrackingData['ai_response'] = $e;
            $usageTrackingData['promptResponseData'] = $res;
            $this->trackUsage('failed', $usageTrackingData);

            \Sentry\captureException($e);
            log_message('error', $errorCode);
            log_message('error', $errorMsg);
            return $this->response->setStatusCode($errorCode, $errorMsg);
        }

        // btutilDebug($res_prompt['data']);
        $res = [
            'success' => $res_prompt['success'],
            'message' => $res_prompt['message'],
            'data' => $this->filterOutput($res_prompt['data']->choices[0]->message->content),
            'usageCounter' => json_encode($res_prompt['data']->usage),
        ];

        // chat token usage
        $url = getenv('API_URL') . '/e/set-chat-usage';
        $ch_data = [
            'aiwp_logged_in' => btflag_cookie('aiwp_logged_in', ''),
            'app' => 'chatbot',
            'prompt_token' => $res_prompt['data']->usage->prompt_tokens,
            'total_token' => $res_prompt['data']->usage->total_tokens,
            'model' => $appParams['model'],
        ];
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($ch_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type:text/plain'));
        $response = curl_exec($ch);
        curl_close($ch);

        // Update aiResponses and conversation
        $aiResponses[] = trim($res['data']);
        btsessionSet($this->sessionName, ["ai_responses" => $aiResponses]);
        $conversation[] = ["role" => "assistant", "content" => trim($res['data'])];
        btsessionSet($this->sessionName, ["conversation" => $conversation]);

        $usageTrackingData['ai_response'] = $res_prompt['data']->choices[0]->message->content;
        $usageTrackingData['response_usage'] = json_encode($res['usageCounter']);
        $usageTrackingData['promptResponseData'] = $res_prompt;
        $usageTrackingData['prompts'] = [
            'user' => $userPrompt,
            'other' => json_encode([
                'trainedPrompt' => $trainedPrompt,
                'suffixPrompt' => $suffixPrompt,
                'conversation' => json_encode($conversation),
            ])
        ];
        $this->trackUsage('success', $usageTrackingData);

        // unset($res['usageCounter']);
        return $res;
    }


    /**
     * textCompletion function
     *
     * @param array $req
     * @return void
     *
     * NOT USED as of March 13, 2023
     */
    private function textCompletion($req = [])
    {
        log_message("debug", "OPENAI Rate Limit: " . getenv('OPENAI_RATE_LIMIT'));

        $res = $this->appResponse;
        if (!isset($req['prompt'])) {
            return $res;
        }

        // if (!$this->debugMode) {
        //     if (btsessionHas($this->sessionName) && btsessionGet($this->sessionName)->surfToken != $req['surfToken']) {
        //         return $this->response->setJSON($res);
        //     }
        // }

        $date_now = date('F d, Y', time());
        $userPrompt = $req['prompt']; //should be raw
        $prompt = $userPrompt;
        $trainedPrompt = "";
        $suffixPrompt = "";
        $appParams = [
            'stop' => '\n\n',
        ];

        $slug = $req['slug'];
        // log_message('debug', btsessionHas($this->sessionName));
        $userPrompts = (btsessionHas($this->sessionName)) ? btsessionGet($this->sessionName)->user_prompts : [];
        $conversation = (btsessionHas($this->sessionName)) ? btsessionGet($this->sessionName)->conversation : [];
        $aiResponses = (btsessionGet($this->sessionName) && isset(btsessionGet($this->sessionName)->ai_responses)) ? btsessionGet($this->sessionName)->ai_responses : [];

        $validateAsk = $this->validateAsk($prompt);
        if($validateAsk) return $validateAsk;
        $userPrompts[] = $prompt;
        btsessionSet($this->sessionName, ["user_prompts" => $userPrompts]);
        $conversation[] = $prompt;
        btsessionSet($this->sessionName, ["conversation" => $conversation]);

        switch ($slug) {
            case 'convert-to-proper-english':
                // $suffixPrompt = '\nConvert the preceding sentence to proper English.';
                $trainedPrompt = 'Convert "' . trim($userPrompt) . '" to proper English.';
                $prompt = $trainedPrompt;
                break;
            case 'chatbot':
            case 'start-chatbot':
                // $prompt = implode("\n", $aiResponses);
                $preventMarkup = '. Respond without using markdown, HTML tags, or any special symbols for formatting.';
                $prompt = implode("\n", btsessionGet($this->sessionName)->conversation);
                $prompt = "Answer \"{$prompt}\" as politely and concisely as possible." + $preventMarkup;

                // $suffixPrompt = `Instructions:\nYou are ${this._assistantLabel}, a large language model trained by OpenAI.
                // Current date: ${currentDate}${this._sepToken}\n\n`;

                $appParams['model'] = "text-davinci-003";
                $appParams['max_tokens'] = 2048;
                // $appParams['presence_penalty'] = 0.9;
                // $appParams['frequency_penalty'] = 0.6;
                // $appParams['echo'] = true; //TODO: i dont get this parameter
                break;
        }

        $usageTrackingData = [
            'requestData' => $req,
            'prompts' => [
                'user' => $userPrompt,
                'other' => json_encode([
                    'trainedPrompt' => $trainedPrompt,
                    'suffixPrompt' => $suffixPrompt,
                ])
            ],
            'app' => $slug,
        ];

        $this->appClass = new BTAITectalic();
        $this->app->_init($this->appClass);
        $this->app->apiKey = getenv('OPENAI_API_KEY');
        $this->app->base64Code = "1J3JfcGUH7BGtSe7";

        if ($suffixPrompt != "") {
            $appParams['suffix'] = trim($suffixPrompt);
        }

        $res_prompt = '';
        try {
            log_message('debug', $prompt);
            $res_prompt = $this->app->textCompletion($prompt, $appParams);
        } catch (\Exception$e) {
            $errorCode = $e->getCode();
            $errorMsg = $e->getMessage();
            $status = btenumStatusCode();
            if (str_contains($e->getMessage(), '400 (Bad Request)')) {
                $errorCode = '400';
                $errorMsg = $status[$errorCode]->message;
                btsessionSet($this->sessionName, [
                    "user_prompts" => [],
                ]);
            } elseif (str_contains($e->getMessage(), 'Response body parse failed')) {
                $errorCode = '500';
                $errorMsg = $status[$errorCode]->message;
                btsessionSet($this->sessionName, [
                    "user_prompts" => [],
                ]);
            } elseif (str_contains($e->getMessage(), 'Internal Server Error')) {
                $errorCode = '500';
                $errorMsg = $status[$errorCode]->message;
                btsessionSet($this->sessionName, [
                    "user_prompts" => [],
                ]);
            }
            $res = [
                'success' => 0,
                'message' => "[`$errorCode`]" . $errorMsg,
                'data' => $errorMsg,
            ];
            $usageTrackingData['ai_response'] = $e;
            $usageTrackingData['promptResponseData'] = $res;
            $this->trackUsage('failed', $usageTrackingData);

            \Sentry\captureException($e);
            // throw new \RuntimeException($e->getMessage(), $e->getCode(), $e);
            log_message('error', $errorCode);
            log_message('error', $errorMsg);
            return $this->response->setStatusCode($errorCode, $errorMsg);
        }

        $res = [
            'success' => $res_prompt['success'],
            'message' => $res_prompt['message'],
            'data' => $this->filterOutput($res_prompt['data']->choices[0]->text, $req['isParagraph']),
            'usageCounter' => json_encode($res_prompt['data']->usage),
        ];
        // if ($suffixPrompt != "") {
        //     $res['usageCounter'] = "<b>Suffix used: </b>" . str_replace('\n', '', $suffixPrompt) . '<br><br>' . $res['usageCounter'];
        // }
        $usageTrackingData['ai_response'] = $res_prompt['data']->choices[0]->text;
        $usageTrackingData['response_usage'] = json_encode($res['usageCounter']);
        $usageTrackingData['promptResponseData'] = $res_prompt;
        $this->trackUsage('success', $usageTrackingData);


        // Update aiResponses and conversation
        $aiResponses[] = trim($res['data']);
        btsessionSet($this->sessionName, ["ai_responses" => $aiResponses]);
        $conversation[] = trim($res['data']);
        btsessionSet($this->sessionName, ["conversation" => $conversation]);
        // unset($res['usageCounter']);
        return $res;
    }

    private function imageGeneration()
    {

    }

    private function imageVariation()
    {

    }

    //---------------------------------------------------------------------------------------------
    //  protected
    //---------------------------------------------------------------------------------------------

    protected function filterOutput($string, $isParagraph="")
    {
        if($isParagraph){
            return nl2br(stripslashes($string));
        }
        // log_message('debug', htmlspecialchars_decode($string));
        $filteredString = htmlspecialchars_decode($string) . "\n\n";
        $filteredString = preg_replace('/\*\*(.*?)\*\*/', '$1', $filteredString);
        $filteredString = preg_replace('/###\s(.+)/', '$1', $filteredString);
        $filteredString = preg_replace('/#\s(.+)/', '$1', $filteredString);
        // $filteredString = htmlspecialchars($filteredString,ENT_QUOTES);
        // $filteredString = htmlentities($filteredString);
        $filteredString = str_replace("&amp;lt;pre&amp;gt;", 'OPENPRETAG', $filteredString);
        $filteredString = str_replace("&amp;lt;/pre&amp;gt;", 'CLOSEPRETAG', $filteredString);
        $filteredString = str_replace('<?php', "OPENPRETAG<?php", $filteredString);
        $filteredString = str_replace('?>', "?>CLOSEPRETAG", $filteredString);
        $filteredString = str_replace('<pre>', "OPENPRETAG", $filteredString);
        $filteredString = str_replace('</pre>', "CLOSEPRETAG", $filteredString);
        // log_message('debug', htmlspecialchars_decode($filteredString));
        $filteredString = htmlspecialchars($filteredString, ENT_NOQUOTES | ENT_HTML5, 'UTF-8');
        $filteredString = str_replace('OPENPRETAG', "<pre>", $filteredString);
        $filteredString = str_replace('CLOSEPRETAG', "</pre>", $filteredString);
        return $filteredString;
    }

}
