{% extends 'base_A_template.twig' %}

{% block head_styles %}
{{ parent() }}
<link rel='stylesheet' href='https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css'>
<link rel='stylesheet' href='https://cdn.form.io/formiojs/formio.full.min.css'>

<style>
{#  #}
</style>
{% endblock %}


{% block head_scripts %}
{{ parent() }}
<script src='https://cdn.form.io/formiojs/formio.full.min.js?ver={{ GLOBAL_DATA.BUILD }}'></script>
<script type='text/javascript'>
    window.onload = function () {
        var force = 1;
        var builder_template ={
  "title": "Gift Generator",
  "collapsible": false,
  "key": "giftGenerator",
  "type": "panel",
  "label": "Panel",
  "input": false,
  "tableView": false,
  "components": [
    {
      "legend": "Gift Generator",
      "key": "fieldSet",
      "type": "fieldset",
      "label": "Field Set",
      "input": false,
      "tableView": false,
      "components": [
        {
          "label": "Select",
          "widget": "choicesjs",
          "tableView": true,
          "key": "select",
          "type": "select",
          "input": true
        },
        {
          "label": "Number",
          "mask": false,
          "tableView": false,
          "delimiter": false,
          "requireDecimal": false,
          "inputFormat": "plain",
          "truncateMultipleSpaces": false,
          "key": "number",
          "type": "number",
          "input": true
        }
      ]
    },
    {
      "type": "button",
      "label": "Submit",
      "key": "submit",
      "disableOnInvalid": true,
      "input": true,
      "tableView": false
    }
  ]
}
        builder_template = JSON.stringify(builder_template);

        Formio.createForm(document.getElementById('formio'), JSON.parse(builder_template), {}).then(function (form) {
            form.on("change", function (e) {
                console.log(form.schema);
            });
        });
    };
</script>
{% endblock %}


{% block body_header %}
{{ parent() }}
{% endblock %}


{% block body_breadcrumbs %}
{{ parent() }}
{% endblock %}


{% block body_content %}
{{ parent() }}
<div style="padding: 50px">
    <div id='formio'></div>
</div>
{% endblock %}


{% block body_footer %}
{{ parent() }}
{% endblock %}


{% block body_modal %}
{{ parent() }}
{% endblock %}


{% block body_hidden_content %}
{{ parent() }}
{% endblock %}


{% block body_scripts %}
{{ parent() }}
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.1/jquery.min.js"></script>
<script>
    $(document).ready(function () {
        console.log("ready!");
        {# alert('asdf'); #}
    });
</script>
{% endblock %}