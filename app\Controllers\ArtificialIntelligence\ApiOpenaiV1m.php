<?php

namespace App\Controllers\ArtificialIntelligence;

use App\Controllers\ArtificialIntelligence\ApiOpenaiV1 as AIBase;
use <PERSON><PERSON><PERSON>\ArtificialIntelligence as BTAI;
use <PERSON><PERSON>ore\ArtificialIntelligence\Tectalic<PERSON>pen<PERSON>i as BTAITectalic;

/**
 *  This is for Mobile
 *  OpenAI API --> https://platform.openai.com/docs/introduction/overview
 */
class ApiOpenaiV1m extends AIBase
{
    //---------------------------------------------------------------------------------------------
    //  variables
    //---------------------------------------------------------------------------------------------

    protected $app;
    protected $appClass;
    protected $sessionName = '';

    //---------------------------------------------------------------------------------------------
    //  public
    //---------------------------------------------------------------------------------------------

    public function __construct()
    {
        parent::__construct();
        header('Access-Control-Allow-Origin: *');

        $this->app = new BTAI();
    }

    public function v1($module = '')
    {
        header('Access-Control-Allow-Origin: *');
        // btutilDebug($module);
        $res = $this->appResponse;
        $resDeniedAccess = [
            'success' => 0,
            'message' => 'Access denied.',
        ];
        $req = $this->request->getPost();

        switch ($module) {
            case 'chat-completion':
                $res = $this->chatCompletion($req);
                break;
            default:
        }

        return $this->response->setJSON($res);
    }

    //---------------------------------------------------------------------------------------------
    //  private
    //---------------------------------------------------------------------------------------------

    /**
     * chatCompletion function
     *
     * @param array $req
     * @return void
     */
    private function chatCompletion($req = [])
    {
        log_message("debug", "chatCompletion");
        log_message("debug", json_encode($req));

        $res = $this->appResponse;
        if (!isset($req['prompt'])) {
            return $res;
        }

        $date_now = date('F d, Y', time());
        $userPrompt = $req['prompt']; //should be raw
        $prompt = $userPrompt;
        $trainedPrompt = "";
        $suffixPrompt = "";
        $appParams = [
            'stop' => '\n\n',
        ];

        $userPrompts[] = $prompt;
        btsessionSet($this->sessionName, ["user_prompts" => $userPrompts]);
        $conversation[] = ["role" => "user", "content" => $prompt];
        btsessionSet($this->sessionName, ["conversation" => $conversation]);

        log_message("debug", json_encode($conversation));

                $message = [
                    ["role" => "system", "content" => "You are a helpful assistant. Answer as concisely as possible.\nCurrent date: {$date_now}\nKnowledge cutoff: 2021-09-01"],
                    ["role" => "user", "content" => $prompt],
                ];

                $appParams['model'] = "gpt-3.5-turbo";


        $this->appClass = new BTAITectalic();
        $this->app->_init($this->appClass);
        $this->app->apiKey = getenv('OPENAI_API_KEY');
        $this->app->base64Code = "1J3JfcGUH7BGtSe7";

        if ($suffixPrompt != "") {
            $appParams['suffix'] = trim($suffixPrompt);
        }

        $res_prompt = '';
        try {
            // log_message('debug', json_encode($message));
            $res_prompt = $this->app->chatCompletion($message, $appParams);
            // log_message('debug', json_encode($res_prompt));
        } catch (\Exception$e) {
            $errorCode = $e->getCode();
            $errorMsg = $e->getMessage();
            $status = btenumStatusCode();
            if (str_contains($e->getMessage(), '400 (Bad Request)')) {
                $errorCode = '400';
                $errorMsg = $status[$errorCode]->message;
                btsessionSet($this->sessionName, [
                    "user_prompts" => [],
                ]);
            } elseif (str_contains($e->getMessage(), 'Response body parse failed')) {
                $errorCode = '500';
                $errorMsg = $status[$errorCode]->message;
                btsessionSet($this->sessionName, [
                    "user_prompts" => [],
                ]);
            } elseif (str_contains($e->getMessage(), 'Internal Server Error')) {
                $errorCode = '500';
                $errorMsg = $status[$errorCode]->message;
                btsessionSet($this->sessionName, [
                    "user_prompts" => [],
                ]);
            }
            $res = [
                'success' => 0,
                'message' => "[`$errorCode`]" . $errorMsg,
                'data' => $errorMsg,
            ];
            $usageTrackingData['ai_response'] = $e;
            $usageTrackingData['promptResponseData'] = $res;
            $this->trackUsage('failed', $usageTrackingData);

            \Sentry\captureException($e);
            log_message('error', $errorCode);
            log_message('error', $errorMsg);
            return $this->response->setStatusCode($errorCode, $errorMsg);
        }

        // btutilDebug($res_prompt['data']);
        $res = [
            'success' => $res_prompt['success'],
            'message' => $res_prompt['message'],
            'data' => $this->filterOutput($res_prompt['data']->choices[0]->message->content),
            'usageCounter' => json_encode($res_prompt['data']->usage),
        ];

        // Update aiResponses and conversation
        $aiResponses[] = trim($res['data']);
        btsessionSet($this->sessionName, ["ai_responses" => $aiResponses]);
        $conversation[] = ["role" => "assistant", "content" => trim($res['data'])];
        btsessionSet($this->sessionName, ["conversation" => $conversation]);




        return $res;
    }




    //---------------------------------------------------------------------------------------------
    //  protected
    //---------------------------------------------------------------------------------------------



}
