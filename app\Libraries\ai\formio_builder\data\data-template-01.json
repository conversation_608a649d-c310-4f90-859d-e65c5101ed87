{
  "components": [
    {
      "label": "Columns",
      "columns": [
        {
          "components": [
            {
              "title": "App Info",
              "collapsible": false,
              "key": "appInfo1",
              "type": "panel",
              "label": "Panel",
              "input": false,
              "tableView": false,
              "components": [
                {
                  "label": "App Title",
                  "tableView": true,
                  "truncateMultipleSpaces": true,
                  "validateOn": "blur",
                  "validate": {
                    "required": true
                  },
                  "key": "appTitle",
                  "type": "textfield",
                  "input": true
                },
                {
                  "label": "Description",
                  "autoExpand": false,
                  "tableView": true,
                  "truncateMultipleSpaces": true,
                  "validateOn": "blur",
                  "validate": {
                    "required": true
                  },
                  "key": "appDescription",
                  "type": "textarea",
                  "input": true
                },
                {
                  "label": "URL ID",
                  "disabled": true,
                  "tableView": true,
                  "validate": {
                    "required": true
                  },
                  "key": "appId",
                  "type": "textfield",
                  "input": true,
                  "defaultValue": appId
                }
              ]
            },
            {
              "label": "HTML",
              "attrs": [
                {
                  "attr": "",
                  "value": ""
                }
              ],
              "content": "<hr>",
              "refreshOnChange": false,
              "key": "html1",
              "type": "htmlelement",
              "input": false,
              "tableView": false
            },
            {
              "label": "Save",
              "showValidations": false,
              "size": "lg",
              "block": true,
              "disableOnInvalid": true,
              "tableView": false,
              "key": "submitSave",
              "type": "button",
              "saveOnEnter": false,
              "input": true
            }
          ],
          "width": 4,
          "offset": 0,
          "push": 0,
          "pull": 0,
          "size": "md",
          "currentWidth": 4
        },
        {
          "components": [
            {
              "label": "Tabs",
              "components": [
                {
                  "label": "Form",
                  "key": "tabForm",
                  "components": [
                    {
                      "label": "Form",
                      "key": "form",
                      "type": "well",
                      "input": false,
                      "tableView": false,
                      "components": [
                        {
                          "label": "HTML",
                          "attrs": [
                            {
                              "attr": "",
                              "value": ""
                            }
                          ],
                          "content": "<div id='builder'></div>",
                          "refreshOnChange": false,
                          "key": "html",
                          "type": "htmlelement",
                          "input": false,
                          "tableView": false
                        }
                      ]
                    }
                  ]
                },
                {
                  "label": "Response Instructions",
                  "key": "tabResponses",
                  "components": [
                    {
                      "label": "Edit Grid",
                      "openWhenEmpty": true,
                      "hideLabel": true,
                      "tableView": false,
                      "templates": {
                        "header": "",
                        "row": "<div class=\"row\">\r\n\t<div class=\"col-sm-1\">#1</div>\r\n\t<div class=\"col-sm-9\">\r\n\t{% util.eachComponent(components, function(component) { %}\r\n\t\t{% if (component.btform.key == \"midPrompt\") { %}\r\n\t\t\t\"{{ getView(component, row[component.key]) }}\" \r\n\t\t{% } else { %}\r\n\t\t\t{{ getView(component, row[component.key]) }} \r\n\t\t{% } %}\r\n\t{%  }) %} \r\n\t</div>\r\n\t{% if (!instance.options.readOnly && !instance.disabled) { %}\r\n\t<div class=\"col-sm-2\" >\r\n\t\t<div class=\"btn-group pull-right\">\r\n\t\t\t<button class=\"btn btn-default btn-light btn-sm editRow\"><i class=\"{{ iconClass('edit') }}\"></i></button>\r\n\t\t\t{% if (!instance.hasRemoveButtons || instance.hasRemoveButtons()) { %}\r\n\t\t\t<button class=\"btn btn-danger btn-sm removeRow\"><i class=\"{{ iconClass('trash') }}\"></i></button>\r\n\t\t\t{% } %}\r\n\t\t</div>\r\n\t</div>\r\n\t{% } %}\r\n</div>"
                      },
                      "rowDrafts": false,
                      "key": "editGrid",
                      "type": "editgrid",
                      "displayAsTable": false,
                      "input": true,
                      "components": [
                        {
                          "legend": "Response #1",
                          "key": "fieldSet",
                          "type": "fieldset",
                          "label": "Field Set",
                          "input": false,
                          "tableView": false,
                          "components": [
                            {
                              "label": "Specific Instruction:",
                              "labelPosition": "left-right",
                              "tableView": true,
                              "validate": {
                                "required": true
                              },
                              "key": "textField",
                              "type": "textfield",
                              "labelWidth": 25,
                              "input": true,
                              "btform": {
                                "type": "textfield",
                                "key": "prePrompt"
                              }
                            },
                            {
                              "label": "User/Response Prompt: ",
                              "labelPosition": "left-right",
                              "widget": "choicesjs",
                              "tableView": true,
                              "multiple": true,
                              "dataSrc": "json",
                              "data": {
                                "json": [
                                  "apple",
                                  "banana",
                                  "orange"
                                ]
                              },
                              "dataType": "string",
                              "validate": {
                                "required": true,
                                "multiple": true
                              },
                              "key": "select",
                              "type": "select",
                              "labelWidth": 25,
                              "input": true,
                              "btform": {
                                "type": "select",
                                "key": "midPrompt"
                              }
                            },
                            {
                              "label": "Detailed Instructions:",
                              "labelPosition": "left-right",
                              "tooltip": "Provide a clear structure to follow which will help the model to generate a more informative response.",
                              "autoExpand": false,
                              "tableView": true,
                              "validate": {
                                "required": false
                              },
                              "key": "textArea",
                              "type": "textarea",
                              "labelWidth": 25,
                              "rows": 2,
                              "input": true,
                              "btform": {
                                "type": "textarea",
                                "key": "postPrompt"
                              }
                            }
                          ]
                        }
                      ],
                      "keyModified": true
                    }
                  ]
                }
              ],
              "key": "tabs1",
              "type": "tabs",
              "input": false,
              "tableView": false
            }
          ],
          "width": 8,
          "offset": 0,
          "push": 0,
          "pull": 0,
          "size": "md",
          "currentWidth": 8
        }
      ],
      "key": "columns",
      "type": "columns",
      "input": false,
      "tableView": false
    }
  ]
}