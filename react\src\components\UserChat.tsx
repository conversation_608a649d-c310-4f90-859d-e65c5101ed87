import type { Message } from "../types/message";

export default function UserChat({ message }: { message: Message }) {
  return (
    <div className="align-start mx-auto flex w-[733px] max-w-full justify-end gap-[18px] px-[30px] md:px-0">
      <div className="max-w-[470px] rounded-[20px] bg-[#F6F6F6] p-2 px-[16px] py-[8px] break-words text-black dark:bg-[#22252A] dark:text-white/80">
        {message.content}
      </div>
      <div className="min-w-[35px]">
        <div
          title="User icon"
          className="relative flex h-[35px] w-[35px] items-center justify-center rounded-full bg-gray-100/50 text-gray-500 dark:bg-[#181818] dark:text-white"
          role="img"
        >
          <svg
            stroke="currentColor"
            fill="currentColor"
            stroke-width="0"
            viewBox="0 0 448 512"
            height="1em"
            width="1em"
            xmlns="http://www.w3.org/2000/svg"
            role="img"
            aria-label="User icon"
          >
            <path
              role="img"
              aria-label="User icon"
              d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512H418.3c16.4 0 29.7-13.3 29.7-29.7C448 383.8 368.2 304 269.7 304H178.3z"
            ></path>
          </svg>
        </div>
      </div>
    </div>
  );
}
