{"builder": {"basic": false, "advanced": false, "data": false, "premium": false, "layout": false, "builderFormFields": {"title": "Form Fields", "default": true, "weight": 0, "components": {"formTitle": {"title": "Form Title", "key": "formTitle", "icon": "font", "weight": 0, "schema": {"tag": "h2", "label": "Form Title", "attrs": [], "content": "Title of Form", "refreshOnChange": false, "key": "formTitle", "type": "htmlelement", "input": false, "tableView": false, "btwizard": {"type": "form_title"}, "hidden_tabs": ["api", "logic", "layout"]}}, "section": {"title": "Panel: <PERSON><PERSON>", "key": "section", "icon": "list-alt", "weight": 0, "schema": {"title": "Slide X", "tableView": false, "type": "panel", "key": "section", "input": false, "property": "section"}}, "text": {"title": "Text", "key": "text", "icon": "terminal", "weight": 1, "schema": {"label": "Text Field", "description": "[FIELD X]", "tableView": false, "type": "textfield", "key": "text", "input": true, "validate": {"maxLength": 255}, "btwizard": {"type": "text"}, "hidden_tabs": ["data"], "show_inputs": ["label", "placeholder", "description"], "descriptionField": {"type": "text", "placeholder": "[FIELD X]", "value": "", "required": true}}, "components": [{"key": "data", "ignore": true}]}, "number": {"title": "Number", "key": "number", "icon": "hashtag", "weight": 2, "schema": {"label": "Number", "description": "[FIELD X]", "mask": false, "tableView": false, "delimiter": false, "requireDecimal": false, "inputFormat": "plain", "truncateMultipleSpaces": false, "key": "number", "type": "number", "input": true, "btwizard": {"type": "number"}, "hidden_tabs": ["api", "logic"], "hidden_inputs": ["labelPosition", "prefix", "attributes", "overlay"]}}, "dropdown": {"title": "Dropdown", "key": "dropdown", "icon": "list", "weight": 3, "schema": {"label": "Dropdown", "description": "[FIELD X]", "tableView": false, "type": "select", "key": "dropdown", "input": true, "btwizard": {"type": "dropdown"}}}, "date": {"title": "Date", "key": "date", "icon": "calendar", "weight": 4, "schema": {"label": "Date", "description": "[FIELD X]", "format": "MMMM dd, yyyy", "tableView": false, "type": "datetime", "key": "date", "input": true, "enableTime": false, "property": "date", "btwizard": {"type": "date"}}}, "textarea": {"title": "Textarea", "key": "textarea", "icon": "font", "weight": 5, "schema": {"label": "Text Area", "description": "[FIELD X]", "tableView": false, "type": "textarea", "key": "textarea", "input": true, "btwizard": {"type": "textarea"}}}, "radio": {"title": "Radio", "key": "radio", "icon": "dot-circle-o", "weight": 6, "schema": {"label": "Radio", "optionsLabelPosition": "right", "description": "[FIELD X]", "inline": false, "tableView": false, "values": [], "type": "radio", "key": "radio", "input": true, "btwizard": {"type": "radio"}}}, "checkbox": {"title": "Checkbox", "key": "checkbox", "icon": "check-square", "weight": 7, "schema": {"label": "Checkbox", "description": "[FIELD X]", "inline": false, "tableView": false, "values": [], "type": "checkbox", "key": "checkbox", "input": true, "defaultValue": false, "btwizard": {"type": "checkbox"}}}, "signature": {"title": "Signature", "key": "signature", "icon": "pencil", "weight": 8, "schema": {"label": "Text Field", "description": "[FIELD X]", "tableView": false, "type": "signature", "key": "signature", "input": true, "btwizard": {"type": "submit"}}}, "us_state": {"title": "US State", "key": "us_state", "icon": "th-list", "weight": 9, "schema": {"label": "US State", "description": "[FIELD X]", "widget": "html5", "tableView": false, "dataSrc": "custom", "key": "us_state", "type": "select", "input": true, "btwizard": {"type": "us_state"}}}, "heading": {"title": "Heading", "key": "heading", "icon": "code", "weight": 10, "schema": {"label": "Heading", "attrs": [], "content": "Please review your document before submitting.", "refreshOnChange": false, "key": "heading", "type": "htmlelement", "input": false, "tableView": false, "btwizard": {"type": "heading"}}}}}, "builderLtr2Pdf": {"title": "LTR2PDF Fields", "default": true, "components": {"ltr2pdfTitle": {"title": "Title", "key": "ltr2pdfTitle", "icon": "font", "weight": 10, "schema": {"label": "Title of Form", "customClass": "form-header", "html": "<h2 style=\"text-align:center;\"><span style=\"font-family:'Times New Roman', Times, serif;\"><strong>TITLE &nbsp;</strong></span></h2>", "tableView": false, "type": "content", "key": "ltr2pdfTitle", "input": false, "refreshOnChange": true, "modules": {"toolbar": ["bold"]}}}, "ltr2pdfContent": {"title": "Content", "key": "ltr2pdfContent", "icon": "font", "weight": 20, "schema": {"label": "Content", "customClass": "form-body", "html": "<p><span style=\"font-family:'Times New Roman', Times, serif;\">Paragraph X</span></p>", "tableView": false, "type": "content", "key": "ltr2pdfContent", "input": false, "refreshOnChange": true}}}}}}