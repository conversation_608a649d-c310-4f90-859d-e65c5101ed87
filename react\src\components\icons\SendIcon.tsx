import type { IconProps } from "./IconProps";

export default function Icon({
  className,
  width = "38",
  height = "38",
  fill = "currentColor",
  size = null,
}: IconProps) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width={size || width}
      height={size || height}
      viewBox="0 0 38 38"
      fill="none"
    >
      <rect x="0.5" y="0.5" width="37" height="37" rx="18.5" fill="#1A1D21" />
      <path
        d="M27.0806 10.4153L10.5898 17.6028C10.2978 17.7421 10.3067 18.1599 10.6032 18.2901L15.064 20.8103C15.329 20.9585 15.6569 20.9271 15.886 20.7294L24.6817 13.1466C24.7401 13.0972 24.8794 13.0028 24.9333 13.0567C24.9917 13.1151 24.9019 13.2499 24.8524 13.3083L17.2427 21.8794C17.0315 22.1175 17.0001 22.4679 17.1708 22.7374L20.0862 27.4138C20.23 27.6968 20.6388 27.6923 20.769 27.4048L27.5882 10.914C27.7364 10.5905 27.3995 10.2626 27.0806 10.4153Z"
        fill={fill}
      />
    </svg>
  );
}
