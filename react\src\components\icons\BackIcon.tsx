import type { IconProps } from "./IconProps";

export default function Icon({
  className,
  width = "14",
  height = "14",
  fill = "currentColor",
  size = "",
}: IconProps) {
  return (
    <svg
      className={className}
      width={size || width}
      height={size || height}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.0386 2.69275L13.0386 11.308C13.0385 11.7669 12.8562 12.2071 12.5317 12.5316C12.2072 12.8561 11.767 13.0384 11.3081 13.0385L8.84619 13.0385L8.74561 13.0287C8.51767 12.9821 8.34619 12.7801 8.34619 12.5385C8.34619 12.2968 8.51768 12.0948 8.74561 12.0482L8.84619 12.0385L11.3081 12.0385C11.5018 12.0384 11.6877 11.9616 11.8247 11.8246C11.9617 11.6876 12.0385 11.5017 12.0386 11.308L12.0386 2.69275C12.0386 2.49894 11.9618 2.31222 11.8247 2.17517C11.6877 2.03827 11.5018 1.96138 11.3081 1.9613L8.84619 1.9613L8.74561 1.95154C8.5177 1.90497 8.34619 1.70297 8.34619 1.4613C8.3463 1.21977 8.51782 1.01864 8.74561 0.972046L8.84619 0.961303L11.3081 0.961304C11.767 0.961383 12.2072 1.1437 12.5317 1.46814C12.8563 1.79272 13.0386 2.23372 13.0386 2.69275ZM4.89449 10.4301C4.72363 10.601 4.45967 10.6227 4.26559 10.4946L4.18746 10.4301L1.14295 7.38562C1.03213 7.2938 0.961502 7.15506 0.961502 6.99999C0.961502 6.84807 1.02928 6.71184 1.13624 6.62003L4.18746 3.5688L4.26559 3.50532C4.45961 3.37704 4.72355 3.39807 4.89449 3.5688C5.08976 3.76406 5.08976 4.08155 4.89449 4.27681L2.67211 6.49999L8.84627 6.49999C9.12241 6.49999 9.34627 6.72385 9.34627 6.99999C9.34627 7.27613 9.12241 7.49999 8.84627 7.49999L2.67139 7.49999L4.89449 9.7231L4.95895 9.80122C5.08711 9.9953 5.06536 10.2593 4.89449 10.4301Z"
        fill={fill}
      />
    </svg>
  );
}
