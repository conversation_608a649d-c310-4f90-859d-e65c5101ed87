{% extends 'base_avalanche_00.twig' %}
{% block styles %}
  {{ parent() }}
  <link rel='stylesheet' href='https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css?ver={{ GLOBAL_DATA.BUILD }}' />
  <link rel='stylesheet' href='https://cdn.form.io/formiojs/formio.full.min.css?ver={{ GLOBAL_DATA.BUILD }}' />
  <link rel='stylesheet' href='{{GLOBAL_DATA.BASE_URL}}/themes/avalanche/css/builder/main.css?ver={{ GLOBAL_DATA.BUILD }}'>
  <style>
    .component-edit-container {
        overflow: visible !important;
    }
  </style>
{% endblock %}
{% block pre_scripts %}
  {{ parent() }}
  <script src='https://cdn.form.io/formiojs/formio.full.min.js?ver={{ GLOBAL_DATA.BUILD }}'></script>
  <script type="text/javascript"  src="{{ VIEW_DATA.app_assets }}js/customFormio.js"></script>
  <script type='text/javascript'>
		window.onload = function () {
			Formio.builder(document.getElementById('builder'), {}, {})
      .then(function (form) {
        form.on("change", function (e) {
          console.log(JSON.stringify(e));
				});
      });

      Formio.createForm(document.getElementById('formio'),
        {"components":[{"label":"HTML","labelWidth":"","labelMargin":"","tag":"p","className":"","attrs":[{"attr":"","value":""}],"content":"{{ VIEW_DATA.form_title }}","refreshOnChange":false,"customClass":"","hidden":false,"modalEdit":false,"key":"html","tags":[],"properties":{},"conditional":{"show":null,"when":null,"eq":"","json":""},"customConditional":"","logic":[],"attributes":{},"overlay":{"style":"","page":"","left":"","top":"","width":"","height":""},"type":"htmlelement","input":false,"tableView":false,"placeholder":"","prefix":"","suffix":"","multiple":false,"defaultValue":null,"protected":false,"unique":false,"persistent":false,"clearOnHide":true,"refreshOn":"","redrawOn":"","dataGridLabel":false,"labelPosition":"top","description":"","errorLabel":"","tooltip":"","hideLabel":false,"tabindex":"","disabled":false,"autofocus":false,"dbIndex":false,"customDefaultValue":"","calculateValue":"","calculateServer":false,"widget":null,"validateOn":"change","validate":{"required":false,"custom":"","customPrivate":false,"strictDateValidation":false,"multiple":false,"unique":false},"allowCalculateOverride":false,"encrypted":false,"showCharCount":false,"showWordCount":false,"allowMultipleMasks":false,"addons":[],"id":"e0hi2f"},{"label":"Submit","labelWidth":"","labelMargin":"","action":"submit","showValidations":false,"theme":"success","size":"md","block":false,"leftIcon":"","rightIcon":"","shortcut":"","description":"","tooltip":"","customClass":"","tabindex":"","disableOnInvalid":true,"hidden":false,"autofocus":false,"disabled":false,"tableView":false,"modalEdit":false,"key":"submit","tags":[],"properties":{},"conditional":{"show":null,"when":null,"eq":"","json":""},"customConditional":"","logic":[],"attributes":{},"overlay":{"style":"","page":"","left":"","top":"","width":"","height":""},"type":"button","input":true,"placeholder":"","prefix":"","suffix":"","multiple":false,"defaultValue":null,"protected":false,"unique":false,"persistent":false,"clearOnHide":true,"refreshOn":"","redrawOn":"","dataGridLabel":true,"labelPosition":"top","errorLabel":"","hideLabel":false,"dbIndex":false,"customDefaultValue":"","calculateValue":"","calculateServer":false,"widget":{"type":"input"},"validateOn":"change","validate":{"required":false,"custom":"","customPrivate":false,"strictDateValidation":false,"multiple":false,"unique":false},"allowCalculateOverride":false,"encrypted":false,"showCharCount":false,"showWordCount":false,"allowMultipleMasks":false,"addons":[],"id":"ertv32r","saveOnEnter":false}]},
        {}
      ).then(function (form) {
        form.on("change", function (e) {
          console.log(form.schema);
        });
      });
		};


	</script>
{% endblock %}
{% block body_header %}
  <header class="app-header fixed-top">
    <div class="app-header-inner">
      <div class="container-fluid py-2">
        <div class="app-header-content">
          <div class="row justify-content-between align-items-center">
            <div class="col-auto">
              <a id="sidepanel-toggler" class="sidepanel-toggler d-inline-block d-xl-none" href="#">
                <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewbox="0 0 30 30" role="img">
                  <title>
                    Menu


                  </title>
                  <path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div id="app-sidepanel" class="app-sidepanel sidepanel-hidden">
      <div id="sidepanel-drop" class="sidepanel-drop"></div>
      <div class="sidepanel-inner d-flex flex-column">
        <a href="#" id="sidepanel-close" class="sidepanel-close d-xl-none">
          ×




        </a>
        <div style="padding: 5px">
          <div id='formio'></div>
        </div>
        <button id="publishToDev" style="margin-right: 10px; margin-left: 10px;" class="btn btn-success">
          Publish



        </button>
      </div>
    </div>
  </header>
{% endblock %}
{% block body_modal %}
  {{ parent() }}
{% endblock %}
{% block body_content %}
  <div style="padding: 20px">
    <div id='builder'></div>
  </div>
{% endblock %}
{% block body_footer %}
  {{ parent() }}
{% endblock %}
{% block post_scripts %}
  {{ parent() }}
  <script src="{{ VIEW_DATA.theme_assets }}js/app.js?ver={{ GLOBAL_DATA.BUILD }}"></script>
  <script type='text/javascript'>

    function save() {
      console.log('saving as draft...');

      var slug = "{{ VIEW_DATA.slug }}";
      var url = '{{ GLOBAL_DATA.BASE_URL }}/btwizard/crud/update';
      var formData = new FormData();
      formData.append('data', form_data);
      formData.append('slug', slug);

      fetch(url, { method: 'POST', body: formData })
        .then(function (response) {
          return response.json();
        })
        .then(function (data) {
          //console.log(data);

          if (data.ok) {
            console.log('successful!');
          }
        }
      );


    }

  </script>
{% endblock %}
