{% extends 'base_00.twig' %}
{% block styles %}
  {{ parent() }}
  <link rel='stylesheet' href='https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css?ver={{ GLOBAL_DATA.BUILD }}'>
    <link rel='stylesheet' href='https://cdn.form.io/formiojs/formio.full.min.css?ver={{ GLOBAL_DATA.BUILD }}'>
    {% endblock %}
    {% block pre_scripts %}
      {{ parent() }}
      <script src='https://cdn.form.io/formiojs/formio.full.min.js?ver={{ GLOBAL_DATA.BUILD }}'></script>
      <script type='text/javascript'>
		window.onload = function () {

var builder_template = localStorage.getItem('btwzrdfrmtmplt');
// var builder_template = sessionStorage.getItem('btwzrdfrmtmplt');
if (builder_template == null) {
builder_template = JSON.stringify({{ VIEW_DATA.btwizard_formio_forms | raw }});

localStorage.setItem('btwzrdfrmtmplt', builder_template);
}

Formio.builder(document.getElementById('builder'), JSON.parse(builder_template), {
builder: {
// basic: false,
// advanced: false,
// data: false,
// layout: false,
// premium: false,
customBasic: {
title: 'Components',
default: true,
weight: 0,
components: {
// textfield: true,
// textarea: true,
// number: true,
// checkbox: true,
// selectboxes: true,
// select: true,
// radio: true,
text: {
title: 'Text',
key: 'text',
icon: 'terminal',
weight: 1,
schema: {
label: 'Text Field',
description: '[FIELD X

]',
 tableView: true,
type: 'textfield',
key: 'text',
input: true,
validate: {
maxLength: 255
}
}
},
panelSlide: {
title: 'Panel: Slide Header',
key: 'panelSlide',
icon: 'list-alt',
weight: 2,
schema: {
title: 'Slide X',
tableView: true,
type: 'panel',
key: 'panelSlide',
input: false,
property: 'panelSlide'
}
},
datetimeCustom: {
title: 'Date',
key: 'datetimeCustom',
icon: 'calendar',
weight: 4,
schema: {
label: 'Date',
description: '[FIELD X

]',
 format: 'MMMM dd, yyyy',
tableView: false,
type: 'datetime',
key: 'datetimeCustom',
input: true,
enableTime: false,
property: 'datetimeCustom'
}
},
textareaCustom: {
title: 'Textarea',
key: 'textarea',
icon: 'font',
weight: 5,
schema: {
label: 'Text Area',
description: '[FIELD X

]',
 tableView: true,
type: 'textarea',
key: 'textareaCustom',
input: true
}
} }},
custom : {
title: 'Custom Fields',
default: true,
components: {
usstateSelect: {
title: 'US State',
key: 'usstateSelect',
icon: 'th-list',
weight: 2,
schema: {
label: 'US State',
description: '[FIELD X

]',
 widget: 'html5',
placeholder: 'This is a US State placeholder only',
tableView: true,
dataSrc: 'custom',
key: 'usstateSelect',
type: 'select',
input: true
}
}
}
} }}).then(function (form) {
form.on("change", function (e) {
console.log(e);
localStorage.setItem('btwzrdfrmtmplt', JSON.stringify(e));
// console.log(form.schema);
});});};
	</script>
    {% endblock %}
    {% block body_header %}
      {{ parent() }}
    {% endblock %}
    {% block body_modal %}
      {{ parent() }}
    {% endblock %}
    {% block body_content %}
      {{ parent() }}
      <div style="padding: 50px">
        <div id='builder'></div>
      </div>
    {% endblock %}
    {% block body_footer %}
      {{ parent() }}
    {% endblock %}
    {% block post_scripts %}
      {{ parent() }}
    {% endblock %}
    