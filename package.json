{"name": "btapp", "version": "1.0.0", "description": "BTApp", "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "btlibrary_forms_00_prepare": "gulp --gulpfile ./scripts/btlibrary/gulp-btlibrary_forms-00_prepare.js build", "btlibrary_forms_01_publish_devt": "gulp --gulpfile ./scripts/btlibrary/gulp-btlibrary_forms-01_publish_devt.js build", "build": "cd react && npm install && npm run buildProd"}, "repository": {"type": "git", "url": "git+https://github.com/baytechph/btapp.git"}, "author": "", "license": "ISC", "homepage": "https://github.com/baytechph/btapp#readme", "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "concat": "^1.0.3", "del": "^5.1.0", "gulp": "^4.0.2", "gulp-awspublish": "^6.0.2", "gulp-babel": "^8.0.0", "gulp-clean-css": "^4.3.0", "gulp-concat": "^2.6.1", "gulp-imagemin": "^7.1.0", "gulp-install": "^1.1.0", "gulp-jsonminify": "^1.1.0", "gulp-remove-empty-lines": "^0.1.0", "gulp-remove-logging": "^1.2.0", "gulp-rename": "^2.0.0", "gulp-sourcemaps": "^3.0.0", "gulp-terser": "^2.0.1", "gulp-uglify": "^3.0.2", "targets-webpack-plugin": "^3.0.0", "vue-template-compiler": "^2.6.12"}, "dependencies": {"axios": "^0.26.1", "babel-plugin-inline-dotenv": "^1.7.0", "bootstrap-icons": "^1.9.1", "bootstrap-vue": "^2.22.0", "cross-env": "^7.0.2", "font-awesome": "^4.7.0", "jquery": "^3.6.0", "laravel-mix": "^5.0.5", "laravel-mix-polyfill": "^2.0.0", "sweetalert2": "^11.4.29", "vue": "^2.6.12", "vue-cli": "^2.9.6"}}